import 'package:args/args.dart'; // 使用其中两个类ArgParser和ArgResults
import 'dart:io';
import 'common.dart';
import 'runner.dart';

final ArgParser argParser = ArgParser();
ArgResults? argResults;
late String routPath;

void main(List<String> arguments) async {
  var uri = Platform.script;
  routPath = File(uri.path).parent.path;
  Map<String, String> envVars = Platform.environment;
  initCommands(arguments);
  if (argResults!['help'] == true || argResults!.command == null) {
    print("\n${Logger.yellow("使用：sh aion.sh --dart [command] [options]")}");
    print("");
    print("${Logger.yellow("[Options]")}");
    print("  ${argParser.usage.replaceAll("\n", "\n  ")}");
    print("");
    print(Logger.yellow("[Commands]"));
    argParser.commands.entries.forEach((element) {
      print(" ${Logger.blue(element.key)} - ${commands[element.key]?.help}:");
      print("   ${element.value.usage.replaceAll("\n", "\n   ")}");
    });
    return;
  }
  await runners[argResults?.command?.name]?.call(argResults?.command);
}

// 异步输出错误信息到标准错误流
void handleError(String msg) {
  stderr.writeln(msg);
  exitCode = 2; //当程序退出，虚拟机检查exitCode，0 表示Success，1 表示Warnings,2 表示Errors
}

initCommands(List<String> arguments) {
  argParser.addFlag('help', abbr: 'h', help: '查看帮助');
  for (MapEntry<String, Command> entry in commands.entries) {
    entry.value.run = runners[entry.value.name];
    argParser.addCommand(entry.value.name, entry.value.argParser);
  }
  argResults = argParser.parse(arguments);
}
