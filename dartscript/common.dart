import 'dart:io';
import 'package:ansicolor/ansicolor.dart';
import 'package:args/args.dart';
import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';

Map<String, Command> commands = {
  "download": Command(
      name: "download",
      help: "下载解压 sql 文件",
      fileList: [
        "http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/dartscript/download.dart"
      ],
      argParser: ArgParser()
        ..addOption('url', abbr: 'u', help: "bundle 的 url")
        ..addOption('out', abbr: 'o', help: "输出路径")),
  "bsdiff": Command(
      name: "bsdiff",
      help: "生成/还原 bsdiff",
      fileList: [
        "http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/dartscript/bsdiff.dart"
      ],
      argParser: ArgParser()
        ..addOption('file1', help: "file1 的路径")
        ..addOption('file2', help: "file2 的路径")
        ..addOption('out', help: "输出文件路径")
        ..addOption('mode',
            defaultsTo: '0',
            help:
                "0: 输出 file1(基准文件) 和 file2(更新文件) 的 bsdiff；\n1：还原 file1(基准文件) 与 file2(diff文件) 的 更新文件")),
  "fdb": Command(
      name: "fdb",
      help: "将文件夹下多个文件合成一个二进制文件",
      fileList: [
        "http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/dartscript/fdb.dart"
      ],
      argParser: ArgParser()
        ..addOption('dir', abbr: 'd', help: "需要合成 f.db 的文件夹根目录")
        ..addOption('out', abbr: 'o', help: "输出文件路径")),
};

void main() async {
  var uri = Platform.script;
  var routPath = File(uri.path).parent.path;
  for (MapEntry<String, Command> entry in commands.entries) {
    for (String url in entry.value.fileList) {
      await download(url, "$routPath/${entry.value.path}");
    }
  }
}

class Command {
  String name;
  List<String> fileList;
  ArgParser argParser;
  Function? run;
  String help;

  String get path => "${name}.dart";

  Command({
    required this.name,
    required this.fileList,
    required this.argParser,
    this.run,
    required this.help,
  });
}

download(String url, String savePath) async {
  Dio _dio = Dio();
  (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
      (client) {
    client.badCertificateCallback =
        (X509Certificate cert, String host, int port) {
      return true;
    };
    return client;
  };
  await _dio.download(url, savePath,
      onReceiveProgress: (receivedBytes, totalBytes) {});
}

bool textIsEmpty(String? text) {
  bool r = true;
  if (text != null && text.length > 0) {
    r = false;
  }
  return r;
}

bool textIsNotEmpty(String? text) {
  return !textIsEmpty(text);
}

class Logger {
  late AnsiPen _errorPen;
  late AnsiPen _warnPen;
  late AnsiPen _infoPen;
  late AnsiPen _successPen;
  late AnsiPen _tipPen;
  late AnsiPen _subInfoPen;

  static Logger _instance = Logger._internal();

  static Logger get() => _instance;

  Logger._internal() {
    _errorPen = AnsiPen()..red(bold: true);
    _warnPen = AnsiPen()..yellow(bold: true);
    _infoPen = AnsiPen()..white();
    _successPen = AnsiPen()..green(bold: true);
    _tipPen = AnsiPen()..blue(bold: true);
    _subInfoPen = AnsiPen()..gray();
  }

  static void e(String message) {
    print(_instance._errorPen(message));
  }

  static void w(String message) {
    print(_instance._warnPen(message));
  }

  static void i(String message) {
    print(_instance._infoPen(message));
  }

  static String red(String message) => _instance._errorPen(message);

  static String yellow(String message) => _instance._warnPen(message);

  static String white(String message) => _instance._infoPen(message);

  static String green(String message) => _instance._successPen(message);

  static String blue(String message) => _instance._tipPen(message);

  static String grey(String message) => _instance._subInfoPen(message);
}
