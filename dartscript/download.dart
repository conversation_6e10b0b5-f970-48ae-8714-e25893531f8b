import 'dart:io';
import 'dart:typed_data';

import 'package:archive/archive.dart';
import 'package:archive/archive_io.dart';
import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:es_compression/zstd.dart';
import 'package:process_run/shell.dart';

run(args) async {
  var uri = Platform.script;
  var routPath = File(uri.path).parent.path;
  String? url = args?["url"];
  String out = args?["out"] ?? "$routPath/temp";
  if (textIsNotEmpty(url)) {
    try {
      await downloadAndExtractBundle(url!, out);
    } catch (e) {
    }
    stdout.write("下载解压完成：${File(out).absolute.path}\n\n");
  }
}

void parseAionUrl(String url, String outPath) async {
  try {
    await downloadAndExtractBundle(
      url,
      outPath,
    );
  } catch (e) {
  }
}

Future parseBif(String dirPath) async {
  Directory dir = Directory(dirPath);
  String? biFilePath;
  (dir.listSync()).forEach((element) async {
    if (element.path.contains(".f")) {
      biFilePath = element.path;
    }
  });
  print("biFilePath: $biFilePath");
  String checkInfo = String.fromCharCodes(
      GZipDecoder().decodeBytes((await File(biFilePath!).readAsBytes())));
  print("baselineInfo:\n" + checkInfo);
}

const String PATCH_MANIFEST_FILE = 'patch_manifest.json';

class FdbModel {
  String? path;
  int size;
  Uint8List? bytes;

  FdbModel({
    required this.path,
    this.size = 0,
    required this.bytes,
  });

  bool get valid {
    return textIsNotEmpty(path) && bytes != null;
  }
}

/// check the bytes whether is zstd.
/// if bytes is our zstd format, it will be recover to the standard zstd format
bool isZstd(List<int> bytes) {
  bool r = false;
  if (bytes != null && bytes.length > 10) {
    if (bytes[0] == 0x28 &&
        bytes[1] == 0xb5 &&
        bytes[2] == 0x2f &&
        bytes[3] == 0xfd) {
      print("[isZstd] standard zstd");
      r = true;
    } else if (bytes[0] == 0x7e &&
        bytes[1] == 0xbd &&
        bytes[2] == 0x72 &&
        bytes[3] == 0x79) {
      print("[isZstd] fliggy zstd");
      bytes[0] = 0x28;
      bytes[1] = 0xb5;
      bytes[2] = 0x2f;
      bytes[3] = 0xfd;
      r = true;
    }
  }
  return r;
}

Future extractLocalBundle(String path, String extractPath) async {
  var dbzip = File(path);
  Directory outDir = Directory(extractPath);
  if (await outDir.exists()) {
    outDir.delete(recursive: true);
  }
  outDir.createSync(recursive: true);
  final sourceBytes = await dbzip.readAsBytes();
  print("datas.length: ${sourceBytes.length}");

  var decodedBytes;
  if (isZstd(sourceBytes)) {
    final codec = ZstdCodec();
    decodedBytes = codec.decode(sourceBytes);
  } else {
    Archive decodeResult;
    var b0 = int.parse("50", radix: 16);
    var b1 = int.parse("4b", radix: 16);
    var b2 = int.parse("3", radix: 16);
    var b3 = int.parse("4", radix: 16);
    var b8 = int.parse("8", radix: 16);
    var b9 = int.parse("0", radix: 16);
    if (sourceBytes[0] == b0 &&
        sourceBytes[1] == b1 &&
        sourceBytes[2] == b2 &&
        sourceBytes[3] == b3) {
      decodeResult = ZipDecoder().decodeBytes(sourceBytes, password: '147953');
    } else {
      var newBytes = [b0, b1, b2, b3]
        ..addAll(sourceBytes)
        ..[8] = b8
        ..[9] = b9;
      decodeResult = ZipDecoder().decodeBytes(newBytes, password: '147953');
    }
    var fdbFile = decodeResult.findFile("f.db");
    if (fdbFile != null) {
      decodedBytes = fdbFile.content;
    } else {
      {
        try {
          if (!outDir.existsSync()) {
            outDir.createSync(recursive: true);
          }
          String outputPath = outDir.path;
          for (final file in decodeResult.files) {
            final filePath = '$outputPath${Platform.pathSeparator}${file.name}';
            if (!file.isFile || !isWithinOutputPath(outputPath, filePath)) {
              continue;
            }
            final output = OutputFileStream(filePath);
            file.writeContent(output);
            output.close();
          }
        } catch (e, s) {}
      }
    }
  }

  // means it use the newest encode mode
  if (decodedBytes != null) {
    List<FdbModel> fileList = [];
    var reader = InputStream(decodedBytes);
    var length = reader.length - 1;
    while (reader.position < length) {
      final filePathCount = reader.readByte();
      if (filePathCount > 0) {
        final filePath =
            String.fromCharCodes(reader.readBytes(filePathCount).toUint8List());
        final fileCount = reader.readUint32();
        final fileBytes = reader.readBytes(fileCount).toUint8List();
        fileList
            .add(FdbModel(path: filePath, size: fileCount, bytes: fileBytes));
      }
    }
    try {
      for (var element in fileList) {
        if (element.valid) {
          final filePath = element.path;
          final fileCount = element.size;
          final fileBytes = element.bytes!;
          File file = File("${outDir.path}/$filePath");
          if (!(await file.parent.exists())) {
            await file.parent.create(recursive: true);
          }
          await file.writeAsBytes(fileBytes);
        }
      }
    } catch (e, s) {}
    File file = File("${outDir.path}/f.db");
    await file.writeAsBytes(decodedBytes);
  }
  await parseBif(extractPath);
  print("Done!");
}

Future downloadAndExtractBundle(String url, String dir) async {
  if (dir.endsWith("/")) {
    dir = dir.substring(0, dir.length - 1);
  }
  var sourceFile = dir + "/fly.sql";
  Dio _dio = Dio();
  (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
      (client) {
    client.badCertificateCallback =
        (X509Certificate cert, String host, int port) {
      return true;
    };
    return client;
  };
  await _dio.download(url, sourceFile,
      onReceiveProgress: (receivedBytes, totalBytes) {});
  await extractLocalBundle(sourceFile, dir + "/fly-extract");
  var shell = Shell();
  await shell
      .run('''aion dump  --target-dilp ${dir + "/fly-extract"}/*.dilp''');
}

bool textIsEmpty(String? text) {
  bool r = true;
  if (text != null && text.length > 0) {
    r = false;
  }
  return r;
}

bool textIsNotEmpty(String? text) {
  return !textIsEmpty(text);
}
