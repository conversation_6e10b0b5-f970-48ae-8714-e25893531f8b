import 'dart:io';
import 'common.dart';
import 'package:archive/archive_io.dart';

run(args) async {
  var uri = Platform.script;
  var routPath = File(uri.path).parent.path;

  String? dir = args?["dir"];
  String? outPath = args?["out"];

  if (textIsNotEmpty(dir) && textIsNotEmpty(outPath)) {
    Directory rootDir = Directory(dir!);
    List<FileSystemEntity> fileList = rootDir.listSync(recursive: true);
    var writer = OutputFileStream(outPath!);
    fileList.forEach((file) {
      if (file is File) {
        final filePath = file.path;
        final writeFilePath = filePath.replaceAll("${rootDir.path}/", "");
        final filePathBytes = writeFilePath.codeUnits;
        final fileBytes = File(filePath).readAsBytesSync();
        writer.writeByte(filePathBytes.length);
        writer.writeBytes(filePathBytes);
        writer.writeUint32(fileBytes.length);
        writer.writeBytes(fileBytes);
      }
    });
    writer.flush();
    writer.close();
  }
}
