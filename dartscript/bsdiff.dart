import 'dart:io';
import 'dart:typed_data';
import 'common.dart';
import 'package:fbsdiff/fbsdiff.dart';

run(args) async {
  var uri = Platform.script;
  var routPath = File(uri.path).parent.path;

  String? file1Path = args?["file1"];
  String? file2Path = args?["file2"];
  String outPath = args?["out"] ?? "$routPath/fly-p.sql";
  String mode = args?["mode"] ?? "0";

  if (textIsNotEmpty(file1Path) && textIsNotEmpty(file2Path)) {
    File file1 = File(file1Path!);
    File file2 = File(file2Path!);

    if (file1.existsSync() && file2.existsSync()) {
      File out = File(outPath);
      Uint8List file1Data = await file1.readAsBytes();
      Uint8List file2Data = await file2.readAsBytes();
      if (mode == "0") {
        Uint8List diffData = bsdiff(file1Data, file2Data);
        print(Logger.yellow("bsdiff 文件大小：${diffData.length}"));
        await out.writeAsBytes(diffData);
      } else if (mode == "1") {
        Uint8List restoreData = bspatch(file1Data, file2Data);
        print("还原文件大小：${restoreData.length}");
        await out.writeAsBytes(restoreData);
      }
    }
  }
}
