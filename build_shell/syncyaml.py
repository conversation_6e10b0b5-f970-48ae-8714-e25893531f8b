# -*- coding: utf-8 -*-
import sys
import os
import re
import yaml
import time
import shutil


def readText(path):
    f = open(path, "r")
    text = f.read()
    f.close
    return text


def writeText(path, text):
    f = open(path, "w")
    f.write(text)
    f.close()


def async_yaml_0(from_yaml, to_yaml):
    with open(from_yaml, "r") as f:
        data_project = yaml.load(f, Loader=yaml.SafeLoader)

    with open(to_yaml, "r") as f:
        data_aion = yaml.load(f, Loader=yaml.SafeLoader)

    for k, v in data_project['dependency_overrides'].items():
        if data_aion.get('dependency_overrides') is None:
            data_aion['dependency_overrides'] = {}
        data_aion['dependency_overrides'][k] = v

    name = ""
    with open(to_yaml, "w") as f:
        new_yaml = ''
        if data_aion.get('name') is not None:
            name = data_aion['name']
            new_yaml = new_yaml + '%s' % yaml.dump({'name': data_aion['name']})
            del data_aion['name']

        if data_aion.get('description') is not None:
            new_yaml = new_yaml + '%s\n' % yaml.dump({'description': data_aion['description']})
            del data_aion['description']

        if data_aion.get('publish_to') is not None:
            new_yaml = new_yaml + '''# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
%s # Remove this line if you wish to publish to pub.dev''' % (
                yaml.dump({'publish_to': data_aion['publish_to']}).replace("\n", ""))
            del data_aion['publish_to']

        if data_aion.get('version') is not None:
            new_yaml = new_yaml + '''# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
%s
''' % yaml.dump({'version': data_aion['version']})
            del data_aion['version']

        if data_aion.get('environment') is not None:
            new_yaml = new_yaml + '%s\n' % (
                yaml.dump({'environment': data_aion['environment']}).replace("'", '"'))
            del data_aion['environment']

        if data_aion.get('dependencies') is not None:
            new_yaml = new_yaml + '%s\n' % yaml.dump({'dependencies': data_aion['dependencies']})
            del data_aion['dependencies']

        if data_aion.get('dependency_overrides') is not None:
            new_yaml = new_yaml + '%s\n' % yaml.dump(
                {'dependency_overrides': data_aion['dependency_overrides']})
            del data_aion['dependency_overrides']

        if data_aion.get('dev_dependencies') is not None:
            new_yaml = new_yaml + '%s\n' % yaml.dump(
                {'dev_dependencies': data_aion['dev_dependencies']})
            del data_aion['dev_dependencies']

        if data_project.get('flutter') is not None:
            new_yaml = new_yaml + '''# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
'''
            yaml_assets = ""
            has_copy_assets = []
            data_aion['flutter'] = data_project['flutter']
            if("assets" in data_aion['flutter'].keys()):
                yaml_assets = "  assets:\n"
                for v in data_aion['flutter']['assets']:
                    if not v.startswith("packages/"):
                        yaml_assets = yaml_assets + "    - " + v + "\n"
                        dir = v.split("/")[0]
                        if dir not in has_copy_assets:
                            has_copy_assets.append(dir)
                            if os.path.exists(dir):
                                shutil.rmtree(dir)
                            shutil.copytree("../%s" % dir, dir)
                    else:
                        yaml_assets = yaml_assets + "    - " + v.replace("/%s/" % data_project["name"], "/" + name + "/") + "\n"
                        dir = "lib/" + v.split("/")[2]
                        if dir not in has_copy_assets:
                            has_copy_assets.append(dir)
                            if os.path.exists(dir):
                                shutil.rmtree(dir)
                            shutil.copytree("../%s" % dir, dir)
                if (data_aion.get("flutter") is not None) and (
                        data_aion['flutter'].get("assets") is not None):
                    del data_aion['flutter']['assets']
            if data_aion.get("flutter") is not None:
                if len(data_aion['flutter']) > 0:
                    new_yaml = new_yaml + '%s\n' % yaml.dump({'flutter': data_aion['flutter']})
                else:
                    new_yaml = new_yaml + 'flutter:\n'
                del data_aion['flutter']
            if len(yaml_assets) > 0:
                new_yaml = new_yaml + '%s\n' % yaml_assets
            new_yaml = new_yaml + '''  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
            '''

        if len(data_aion) > 0:
            new_yaml = new_yaml + yaml.dump(data_aion)

        f.write(new_yaml)


def async_yaml_1(from_yaml, to_yaml):
    with open(from_yaml, "r") as f:
        data_project = yaml.load(f, Loader=yaml.SafeLoader)

    with open(to_yaml, "r") as f:
        data_aion = yaml.load(f, Loader=yaml.SafeLoader)

    for k, v in data_project['dependency_overrides'].items():
        if data_aion.get('dependency_overrides') is None:
            data_aion['dependency_overrides'] = {}
        data_aion['dependency_overrides'][k] = v

    with open(to_yaml, "w") as f:
        new_yaml = ''
        if data_aion.get('name') is not None:
            new_yaml = new_yaml + '%s' % yaml.dump({'name': data_aion['name']})
            del data_aion['name']

        if data_aion.get('description') is not None:
            new_yaml = new_yaml + '%s\n' % yaml.dump({'description': data_aion['description']})
            del data_aion['description']

        if data_aion.get('publish_to') is not None:
            new_yaml = new_yaml + '''# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
%s # Remove this line if you wish to publish to pub.dev''' % (
                yaml.dump({'publish_to': data_aion['publish_to']}).replace("\n", ""))
            del data_aion['publish_to']

        if data_aion.get('version') is not None:
            new_yaml = new_yaml + '''# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
%s
''' % yaml.dump({'version': data_aion['version']})
            del data_aion['version']

        if data_aion.get('environment') is not None:
            new_yaml = new_yaml + '%s\n' % (
                yaml.dump({'environment': data_aion['environment']}).replace("'", '"'))
            del data_aion['environment']

        if data_aion.get('dependencies') is not None:
            new_yaml = new_yaml + '%s\n' % yaml.dump({'dependencies': data_aion['dependencies']})
            del data_aion['dependencies']

        if data_aion.get('dependency_overrides') is not None:
            new_yaml = new_yaml + '%s\n' % yaml.dump(
                {'dependency_overrides': data_aion['dependency_overrides']})
            del data_aion['dependency_overrides']

        if data_aion.get('dev_dependencies') is not None:
            new_yaml = new_yaml + '%s\n' % yaml.dump(
                {'dev_dependencies': data_aion['dev_dependencies']})
            del data_aion['dev_dependencies']

        if data_aion.get('flutter') is not None:
            new_yaml = new_yaml + '''# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
'''
            new_yaml = new_yaml + '%s\n' % yaml.dump({'flutter': data_aion['flutter']})
            del data_aion['flutter']
            new_yaml = new_yaml + '''  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
            '''

        if len(data_aion) > 0:
            new_yaml = new_yaml + yaml.dump(data_aion)

        f.write(new_yaml)

def update_bizname():
    with open("pubspec.yaml", "r") as f:
        data_project = yaml.load(f, Loader=yaml.SafeLoader)
    old_name = data_project.get('name')

    with open("../pubspec.yaml", "r") as f:
        data_biz_project = yaml.load(f, Loader=yaml.SafeLoader)
    biz_name = data_biz_project.get('name')
    new_name = biz_name + '_' + str(int(time.time() * 1000))

    # modify build_shell/pubspec.yaml
    data1 = readText('pubspec.yaml')
    data1 = re.sub(r"%s" % old_name, new_name, data1)
    writeText('pubspec.yaml', data1)

    # modify build_shell/aion_shell/pubspec.yaml
    data2 = readText('aion_shell/pubspec.yaml')
    data2 = re.sub(r"%s" % old_name, new_name, data2)
    writeText('aion_shell/pubspec.yaml', data2)

    # modify build_shell/aion_shell/lib/main.dart
    data3 = readText('aion_shell/lib/main.dart')
    data3 = re.sub(r"%s" % old_name, new_name, data3)
    writeText('aion_shell/lib/main.dart', data3)

    # modify build_shell/lib/build_shell.dart
    data4 = readText('lib/build_shell.dart')
    data4 = re.sub(r"%s" % old_name, new_name, data4)
    writeText('lib/build_shell.dart', data4)

def run_0():
    update_bizname()
    async_yaml_0("../pubspec.yaml", "pubspec.yaml")


def run_1():
    async_yaml_1("pubspec.yaml", "aion_shell/pubspec.yaml")

if __name__ == '__main__':
    run_0()
    run_1()
