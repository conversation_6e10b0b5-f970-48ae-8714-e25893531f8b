name: flutter_travel_memories_photo_album_1749104347
description: A new Flutter plugin.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 0.0.1

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  aion_sdk:
    sdk: flutter
  flutter:
    sdk: flutter
  flutter_travel_memories_photo_album:
    path: ../../flutter_travel_memories_photo_album

dependency_overrides:
  collection: 1.15.0
  fbridge:
    git:
      ref: '3.7'
      url: **************************:fliggy_mobile/fbridge.git
  fbroadcast:
    git:
      ref: 1.3.0
      url: **************************:fapi/fbroadcast.git
  ficonfont:
    git:
      ref: 1.3.0
      url: **************************:fliggy_mobile/ficonfont.git
  fliggy_mtop:
    git:
      ref: 1.2.3
      url: **************************:fliggy_mobile/fliggy_mtop.git
  fliggy_router:
    git:
      ref: 2.0.0v
      url: **************************:fliggy_mobile/fliggy_router.git
  fliggy_usertrack:
    git:
      ref: 2.0.0v
      url: **************************:fliggy_mobile/fliggy_usertrack.git
  flutter_boost:
    git:
      ref: 4.4.0s
      url: **************************:fliggy_mobile/flutter_boost.git
  flutter_common:
    git:
      ref: 1.1.3
      url: **************************:fliggy_mobile/flutter_common.git
  fsuper: ^2.1.1
  ftoast: ^2.0.0
  petitparser: ^4.3.0
  xml: 5.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter:
  plugin:
    androidPackage: com.taobao.trip.build_shell
    pluginClass: BuildShellPlugin
