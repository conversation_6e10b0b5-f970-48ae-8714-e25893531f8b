import 'dart:async';
import 'package:aion_sdk/aion_sdk.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:fbridge/fbridge.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_travel_memories_photo_album_1749104347/build_shell.dart';

class AionBundleEnv extends AionSDKEnv {

  @override
  Future<AionBundle> loadBundleFromLocal(String bizName) async {
    // 业务自行实现读取方式
    return DefaultAionBundle(bizName, '');
  }

  @override
  Future<AionBundle> loadBundleFromNetwork(String bizName) async {
    // 业务自行实现读取方式
    return DefaultAionBundle(bizName, '');
  }

  @override
  Future<AionBundle> loadBundleFromUrl(String bizName, String? cacheUrl) async {
    /// 业务方自行实现远程加载器
    return DefaultAionBundle(bizName, '');
  }

  @override
  void log(AionBundle? bundle, String tag, String msg) {
    print('Aion log: $bundle, $tag');
  }

  @override
  void onException(AionBundle bundle, code, e, s) {
    print('Aion Exception: $bundle, $e, $s');
  }

  @override
  void onStat(AionBundle? bundle, String logType, Map<String, String> params) {}
}

void main() {
  init();
}

void init() {
  runZonedGuarded(
        () {
      AionSDKManager.instance.setAionSDKEnv(AionBundleEnv());
      runApp(AionApp());
      // 不进行自动设置系统设置
      RendererBinding.instance.renderView.automaticSystemUiAdjustment = false;
    },
        (Object error, StackTrace stackTrace) {
      print("onError: ${error.toString()}${stackTrace.toString()}");
      FBridgeApi.newInstance(null).toast(error.toString(), 0);
    },
  );
}


class AionApp extends StatefulWidget {
  const AionApp({ Key? key }) : super(key: key);

  @override
  _AionAppState createState() => _AionAppState();
}

class _AionAppState extends State<AionApp> {
  @override
  void initState() {
    super.initState();
  }

  Route<dynamic>? routeFactory(
      RouteSettings settings, String? uniqueId){

  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      builder: (BuildContext context, widget) {
        FlutterBoostApp(routeFactory);
        Map<String, AionPageBuilder> biz = getRouteConfig();
        return biz[biz.keys.first]!(biz.keys.first, <String, String>{});
      },
    );
  }
}
