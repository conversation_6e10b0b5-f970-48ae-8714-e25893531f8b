#!/bin/bash

HEADER='\033[95m'
OKBLUE='\033[94m'
OKGREEN='\033[92m'
WARNING='\033[93m'
FAIL='\033[91m'
END='\033[0m'
BOLD="\033[1m"

start_time=`date +%s`

patch_name="fly.sql"

build_shell_path=`pwd`
project_path=`dirname ${build_shell_path}`
project_name=${project_path##*/}
need_check_baseline=0
need_qrcode=0
flutter_path=""
shell_url="http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev"
engin_version="3.7"

if [[ $1 =~ "--" ]]
    then
    echo ""
else
    page_name=$1
fi

if [[ $1 == "--deploy" ]] || [[ $1 == "--deployToOld" ]] || [[ $1 == "--test" ]] || [[ $1 == "--test2" ]] || [[ $1 == "--fbase" ]]
    then
    need_qrcode=1
fi

if [[ $1 == "--fbase" ]]
    then
    need_check_baseline=1
fi

local_ip=`ifconfig -a|grep inet|grep -v 127.0.0.1|grep -v inet6|awk '{print $2}'|tr -d "addr:"`
array=(`echo ${local_ip} | tr '\n' ' '` )
local_ip=${array[0]}

# 用来标识生成的zip包和版本
cur_sec=`date '+%s'`

function logh()
{
    echo "$HEADER $1 $END"
}

function logb()
{
    echo "$OKBLUE $1 $END"
}

function logg()
{
    echo "$OKGREEN $1 $END"
}

function logw()
{
    echo "$WARNING $1 $END"
}

function loge()
{
    echo "$FAIL $1 $END"
}



function get_shell_url() {
  shell_url=$(python3 -c "$(curl -fsSL http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/get_shell_url.py)")
  if [[ $shell_url != "http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev" ]]; then
    echo "⚠️ 脚本仓库地址: $shell_url"
  else
    echo ""
  fi
}

function setup_qrencode() {
  if qrencode --version >/dev/null 2>&1
  then
    echo ""
  else
    logw "[❗️] 检测到 [qrencode] 未安装，别担心，即将开始安装.."
    brew install qrencode
    logg "✅ [qrencode] 安装完成!"
  fi
}

function do_syncyaml() {
  cur_sec=$1
  deploy_mode=$2
  echo "🏃 开始同步 pubspec.yaml.."

  python3 -c "$(curl -fsSL ${shell_url}/flitetools/build_syncyaml.py)" -t=${cur_sec} -deploy=${deploy_mode}
  if [[ $? == 0 ]]
  then
    logg "🍻 pubspec.yaml 同步完成!"
  else
    loge "[❌] 同步 pubspec.yaml 出错！"
    exit 1
  fi
}

function get_time(){
    start=$1
    end=$2

    start_s=$(echo $start | cut -d '.' -f 1)
    end_s=$(echo $end | cut -d '.' -f 1)

    time=$(( (10#$end_s - 10#$start_s)))

    echo "⏰ $3耗时: $time s"
    echo ""
}

function generate_qrcode() {
  aion="1"
  if [[ "$page_name" ]]; then
    if [[ ${page_name} == '/'* ]]
        then
        echo ""
    else
        page_name="/$page_name"
    fi
    demo_url="http://${local_ip}/${patch_name}?un_flutter=true&flutter_path=${page_name}&aion=${aion}&a_ver=${cur_sec}&a_biz=${project_name}"
  else
    demo_url="https://cdn.fliggy.com/flu/fdemo/test/fly.sql?un_flutter=true&flutter_path=/fdemo/test_config&aion=1&a_ver=-1&bizName=${project_name}&cdn=http://${local_ip}/${patch_name}?a_ver=${cur_sec}"
  fi

  logb "$demo_url"

  encodeUrl=$(python3 -c "import urllib.parse, sys; print(urllib.parse.quote(sys.argv[1]))" "$demo_url")

  logb "\n[encode: $encodeUrl ]"

  setup_qrencode
  qrencode -l M -t UTF8 -k "$demo_url"

  echo ""
  logw "💡【提示】直接在 shell 命令后添加页面名称，可快捷生成页面URL二维码 👏👏👏"

  sudo apachectl start
}


function predeploy() {
   echo "🏃 开始发布校验.."
   python3 -c "$(curl -fsSL ${shell_url}/flitetools/aion_deploy.py)" -mode=$1
   if [[ $? == 0 ]]
   then
     logg "🍻 发布校验完成!"
   else
     loge "[❌] 终止发布！请看错误日志.."
     exit 1
   fi
}

function predeployToOld() {
   echo "🏃 开始发布校验.."
   python3 -c "$(curl -fsSL ${shell_url}/flitetools/aion_deploy_to_old.py)" -mode=$1
   if [[ $? == 0 ]]
   then
     logg "🍻 发布校验完成!"
   else
     loge "[❌] 终止发布！请看错误日志.."
     exit 1
   fi
}


function preFbase() {
    echo "🏃 开始校对内置依赖.."
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/pre_fbase.py)"
    pre_fbase_result=$?
    flutter_path=${pre_fbase_result/1/''}
    if [[ $? == 0 ]]
    then
      logg "🍻 内置依赖校对完成!"
    else
      loge "[❌] 终止发布！请看错误日志.."
      exit 1
    fi
}


function upload_cdn() {
  cd build/patch
  if [[ $1 == "--deploy" ]] || [[ $1 == "--deployToOld" ]]
    then
    random_suffix=`openssl rand -base64 16|sed -r "s#[^a-z]##g"`
    cdn_name="${project_name}/${random_suffix}"
    cmd="sh%20aion.sh%20$1=$2"
    if [[ -n "$2" ]]; then
       echo ""
    else
       cmd="sh%20aion.sh%20$1"
    fi
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/upload_cdn.py)" -name=${cdn_name} -mode=$1 -cmd=${cmd} -suffix=${cur_sec}
    if [[ $? != 0 ]]
      then
      exit 1
    fi
  elif [[ $1 == "--test" ]]
      then
      cdn_name="${project_name}/test"
      python3 -c "$(curl -fsSL ${shell_url}/flitetools/upload_cdn.py)" -name=${cdn_name} -mode=$1 -suffix=${cur_sec}
  elif [[ $1 == "--test2" ]]
      then
      random_suffix=$(date +%s)
      cdn_name="${project_name}/test/${random_suffix}"
      python3 -c "$(curl -fsSL ${shell_url}/flitetools/upload_cdn.py)" -name=${cdn_name} -mode=$1 -suffix=${cur_sec}
  elif [[ $1 == "--fbase" ]]
      then
      cdn_name="${project_name}/test"
      python3 -c "$(curl -fsSL ${shell_url}/flitetools/upload_cdn.py)" -name=${cdn_name} -mode=$1 -suffix=${cur_sec}

  fi
}

function get_project_name() {
  project_name=$(python3 -c "$(curl -fsSL ${shell_url}/flitetools/get_proname.py)")
}

function get_engin_version() {
    flutter_script_path=$(whereis flutter | awk '{print $2}')
    flutter_script_path=$(dirname "$flutter_script_path")
    engin_version_path="${flutter_script_path}/internal/engine.version"
    engine_version=$(cat "$engin_version_path")
    # 检查版本号是否以3.7开头
    if [[ "$engine_version" =~ ^3\.7 ]]; then
      engin_version="3.7"
    elif [[ "$engine_version" =~ ^2\.5 ]]; then
      engin_version="2.5"
    fi
}

# workdir: build_shell/
get_shell_url
get_engin_version
if [[ -n "$page_name" ]]; then
  echo ""
  get_project_name
  generate_qrcode
else
  get_project_name

# check baseline info
  if [[ ${need_check_baseline} == 0 ]]
  then
      echo ""
      logb "🏃 开始基线校验..."
      temp_time_start=`date +%s`
      python3 -c "$(curl -fsSL ${shell_url}/flitetools/preaion_build.py)" -mode=$2
      if [[ $? == 0 ]]
      then
        logg "🍻 基线校验完成!"
      else
        loge "[❌] 基线校验出错! 请看日志详情.."
        exit 1
      fi
      temp_time_end=`date +%s`
      get_time ${temp_time_start} ${temp_time_end} "基线校验"
  fi

# pre-deploy
  if [[ $1 == "--deploy" ]]
      then
      temp_time_start=`date +%s`
      predeploy $2
      temp_time_end=`date +%s`
      get_time ${temp_time_start} ${temp_time_end} "发布校验"
  fi

# pre-deployToOld
  if [[ $1 == "--deployToOld" ]]
      then
      temp_time_start=`date +%s`
      predeployToOld $2
      temp_time_end=`date +%s`
      get_time ${temp_time_start} ${temp_time_end} "发布校验"
  fi


# pre-fbase
  if [[ $1 == "--fbase" ]]
    then
    temp_time_start=`date +%s`
    preFbase
    echo "flutter_path: ${flutter_path}"
    temp_time_end=`date +%s`
    get_time ${temp_time_start} ${temp_time_end} "发布校验"
  fi

# sync yaml
  temp_time_start=`date +%s`
  echo ""
  do_syncyaml $cur_sec $1
  temp_time_end=`date +%s`
  get_time ${temp_time_start} ${temp_time_end} "同步依赖"

# enter build_shell/aion_shell
  cd aion_shell

# pub get
  echo ""
  temp_time_start=`date +%s`
  logb "🏃 flutter pub get"
  if [ -f "${build_shell_path}/aion_shell/pubspec.lock" ]; then
    rm ${build_shell_path}/aion_shell/pubspec.lock
  fi
  if [ -z "$FAION_MTL" ]; then
    ${flutter_path}flutter pub get 2>&1 | tee /dev/tty | grep -q "version solving failed"
    if [[ $? == 0 ]]
    then
      loge "[❌] 依赖存在冲突，请检查 pubspec.yaml!（在该文档中尝试找到答案：https://alidocs.dingtalk.com/i/nodes/G1DKw2zgV2KnvL4kFrqQ55aLJB5r9YAn）"
      exit 1
    else
      logg "🍻 依赖拉取完成!"
    fi
  else
    ${flutter_path}flutter pub get
  fi
  temp_time_end=`date +%s`
  get_time ${temp_time_start} ${temp_time_end} "拉取依赖"

# start aion compile
  echo ""
  temp_time_start=`date +%s`
  if [ -f "${build_shell_path}/aion_shell/.dart_tool/flutter_aion/app.dill.comp.log" ]; then
      rm ${build_shell_path}/aion_shell/.dart_tool/flutter_aion/app.dill.comp.log
  fi
  if [ -f "${build_shell_path}/aion_shell/.dart_config/api_check_cache.config" ]; then
      rm ${build_shell_path}/aion_shell/.dart_config/api_check_cache.config
  fi
  logb "🏃 运行 aion build_bundle"

  compile_success=0
  ${flutter_path}aion build_bundle 2>&1 | tee /dev/tty | egrep -q "(COMPILE ERROR|ProcessException|nhandled exception)"

  if [ $? -eq 0 ]; then
    compile_success=0
  else
    compile_success=1
  fi

  temp_time_end=`date +%s`
  get_time ${temp_time_start} ${temp_time_end} "代码编译"

#   if [[ `grep -c "Aion compiler frontend config load finished" ${build_shell_path}/aion_shell/.dart_tool/flutter_aion/app.dill.comp.log` -ne '0' ]]; then
#   if [ ! -f "${build_shell_path}/aion_shell/.dart_tool/flutter_aion/app.dill.comp.log" ] || [[ `grep -c "Aion compiler frontend config load finished" ${build_shell_path}/aion_shell/.dart_tool/flutter_aion/app.dill.comp.log` -ne '0' ]]; then
  if [[ ${compile_success} == 1 ]]; then
#   if [ ! -f "${build_shell_path}/aion_shell/.dart_tool/flutter_aion/app.dill.comp.log" ] || [[ `grep -c "Aion compiler frontend config load finished" ${build_shell_path}/aion_shell/.dart_tool/flutter_aion/app.dill.comp.log` -ne '0' ]]; then
    # write baseinfo
    temp_time_start=`date +%s`
    echo "🏃 开始写入基线信息.."
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/add_baseinfo.py)" -mode=$2 -deploy=$1
    if [[ $? == 0 ]]
    then
      logg "🍻 写入基线信息完成!"
    else
      loge "[❌] 写入基线信息出错! 请查往上滑动控制台输出的内容，查看错误详情，解决错误后再重试！这是一些有用的提示💡：\n 1. 错误通常可以通过搜索 [error:] 关键字查找到\n 2. 在该文档中尝试找到答案：https://alidocs.dingtalk.com/i/nodes/G1DKw2zgV2KnvL4kFrqQ55aLJB5r9YAn"
      exit 1
    fi
    temp_time_end=`date +%s`
    get_time ${temp_time_start} ${temp_time_end} "写入基线信息"
    end_time=`date +%s`

    # generate qrcode
    if [[ ${need_qrcode} == 0 ]]
    then
      sudo cp build/patch/${patch_name} /Library/WebServer/Documents/
      logg "✅ 本地服务准备完成!"
      logw "🌍 启动本地服务后（运行 sudo apachectl start），你可以在基础 URL 添加参数跳转到指定页面!"
      read -p "输入页面名称[回车, 为默认${project_name}_demo页]：" page_name
      generate_qrcode
    fi

    # deploy
    if [[ $1 == "--deploy" ]] || [[ $1 == "--deployToOld" ]] || [[ $1 == "--test" ]] || [[ $1 == "--test2" ]] || [[ $1 == "--fbase" ]]
        then
        logg "✅ 恭喜！构建完成。产物原始路径："
        logg "  $build_shell_path/aion_shell/build/patch"
        temp_time_start=`date +%s`
        echo "🏃 开始上传动态包到 CDN.."
        upload_cdn $1 $2
        temp_time_end=`date +%s`
        get_time ${temp_time_start} ${temp_time_end} "上传动态包"
    else
        rm -f build/patch/${patch_name}
    fi
  else
    if [[ $engin_version == "2.5" ]]; then
        loge "[❌] 构建动态包产物时出错误，请查看日志详情:"
        loge "  $build_shell_path/aion_shell/.dart_tool/flutter_aion/app.dill.comp.log"
    elif  [[ $engin_version == "3.7" ]]; then
        loge "[❌] 构建动态包产物时出错误，请查往上滑动控制台输出的内容，查看错误详情，解决错误后再重试！这是一些有用的提示💡：\n 1. 错误通常可以通过搜索 [error:] 关键字查找到。若使用三方控制台请确认日志是否截断，需要从执行命令开始到结束的完整日志，若被阶段请使用系统控制台执行。\n 2. 在该文档中尝试找到答案：https://alidocs.dingtalk.com/i/nodes/G1DKw2zgV2KnvL4kFrqQ55aLJB5r9YAn"
    fi
  fi

  echo ""

  faion_start_time="$FAION_START_TIME"
  get_time ${faion_start_time} ${end_time} "全程"
fi

