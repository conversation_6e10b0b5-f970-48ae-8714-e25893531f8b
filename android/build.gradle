group 'com.taobao.trip.flite'
version '1.0'

buildscript {
    repositories {
        mavenLocal()
        maven {
            url "http://mvnrepo.alibaba-inc.com/mvn/repository"
        }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.5.0'
    }
}

rootProject.allprojects {
    repositories {
        mavenLocal()
        maven {
            url "http://mvnrepo.alibaba-inc.com/mvn/repository"
        }
    }
}

apply plugin: 'com.android.library'
apply from: 'https://code.alibaba-inc.com/fliggy_android/buildscript/raw/master/mtl_versions.gradle'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    compileOnly dep.tripCommonservice
    compileOnly 'io.flutter:flutter_embedding_release:1.0.0-a67792536ca236a971d0efbcfd7af4efb8f6c119'

    implementation dep.alibabaFastjson
    implementation(dep.tripCommonui) {
      transitive true
    }
    implementation 'com.taobao.android:uikit_base:*******'

//    compile ('com.taobao.trip:commonui:********-SNAPSHOT@aar') {
//        transitive = true
//    }

}


