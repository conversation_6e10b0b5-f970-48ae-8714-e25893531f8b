package com.taobao.trip.flutter_travel_memories_photo_album;

import android.os.Handler;
import android.os.Looper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fliggy.photoselect.util.FlutterUploadHelper;
import com.taobao.trip.commonservice.evolved.db.DBManager;
import com.taobao.trip.gemini.convert.GlobalExecutorService;

import java.util.Map;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;
import io.flutter.plugin.common.PluginRegistry.Registrar;


/**
 * FlutterTravelMemoriesPhotoAlbumPlugin
 */
public class FlutterTravelMemoriesPhotoAlbumPlugin implements FlutterPlugin, MethodCallHandler {
    @Override
    public void onAttachedToEngine(FlutterPluginBinding flutterPluginBinding) {
        final MethodChannel channel = new MethodChannel(flutterPluginBinding.getFlutterEngine().getDartExecutor(), "flutter_travel_memories_photo_album");
        channel.setMethodCallHandler(new FlutterTravelMemoriesPhotoAlbumPlugin());
    }

    // This static function is optional and equivalent to onAttachedToEngine. It supports the old
    // pre-Flutter-1.12 Android projects. You are encouraged to continue supporting
    // plugin registration via this function while apps migrate to use the new Android APIs
    // post-flutter-1.12 via https://flutter.dev/go/android-project-migration.
    //
    // It is encouraged to share logic between onAttachedToEngine and registerWith to keep
    // them functionally equivalent. Only one of onAttachedToEngine or registerWith will be called
    // depending on the user's project. onAttachedToEngine or registerWith must both be defined
    // in the same class.
    public static void registerWith(Registrar registrar) {
        final MethodChannel channel = new MethodChannel(registrar.messenger(), "flutter_travel_memories_photo_album");
        channel.setMethodCallHandler(new FlutterTravelMemoriesPhotoAlbumPlugin());
    }

    @Override
    public void onMethodCall(MethodCall call, final Result result) {
//        try {
            switch (call.method) {
                case "getPlatformVersion":
                    result.success("Android " + android.os.Build.VERSION.RELEASE);
//          result.success(DBManager.getInstance().getValueFromKey("latLngCityName"));
                    break;
                case "getPhotoCache":
                    // 安卓的猪宝宝遇到db会崩溃，在猪宝宝里不要用db
                    String res = DBManager.getInstance().getValueFromKey((String) call.arguments);
                    result.success(res);
                    break;
                case "uploadPic":
                    JSONObject params = new JSONObject();
                    if (call.arguments instanceof String) {
                        try {
                            params = JSONObject.parseObject((String) call.arguments);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    FlutterUploadHelper uploadPhotos = new FlutterUploadHelper();
                    uploadPhotos.uploadPhotos(params, new FlutterUploadHelper.UploadCallBack() {
                        @Override
                        public void uploadSuccess(final JSONObject sucres) {
                            if (sucres == null) {
                                result.success("result:success");
                            }
                            sucres.put("result", "success");
                            Handler mainHandler = new Handler(Looper.getMainLooper());
                            mainHandler.post(new Runnable() {
                                @Override
                                public void run() {
                                    //已在主线程中，可以更新UI
                                    result.success(sucres);
                                }

                            });
//                            result.success(res);
                        }

                        @Override
                        public void uploadFailed(final JSONObject failres) {
                            Handler mainHandler = new Handler(Looper.getMainLooper());
                            mainHandler.post(new Runnable() {
                                @Override
                                public void run() {
                                    //已在主线程中，可以更新UI
                                    result.success(failres);
                                }

                            });
//                            if (res == null) {
//                                res = new JSONObject();
//                            }
//                            res.put("result", "fail");
//                            result.success(res);
                        }
                    });
                    break;
                case "save2KVCache":
                    // 安卓的猪宝宝遇到db会崩溃，在猪宝宝里不要用db
                    // String key = "{\"precision\":3,\"30.280,119.998\":\"杭州市\"}";
                    // result.success(key);
//                    JSONObject fields = mComponent.getFields();
//                    JSONObject params = new JSONObject();
                    JSONObject fields = new JSONObject();
                    if (call.arguments instanceof String) {
                        try {
                            fields = JSONObject.parseObject((String) call.arguments);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    Map jsonToMap =  JSONObject.parseObject(fields.toJSONString());
//                    fields = (JSONObject)call.arguments;
                    DBManager.getInstance().setKeyValue(jsonToMap.get("key").toString(),jsonToMap.get("value").toString());
                    break;
                default:
                    result.notImplemented();
                    break;

            }
    }

    @Override
    public void onDetachedFromEngine(FlutterPluginBinding binding) {
    }
}
