# -*- coding: utf-8 -*-
import sys
import os
import re
import shutil
import subprocess

HEADER = '\033[95m'
OKBLUE = '\033[94m'
OKGREEN = '\033[92m'
WARNING = '\033[93m'
FAIL = '\033[91m'
ENDC = '\033[0m'
BOLD = "\033[1m"

def infog(msg):
    print(OKGREEN + "[XXX] " + msg + ENDC)

def info(msg):
    print(OKBLUE + "[XXX] " + msg + ENDC)

def warn(msg):
    print(WARNING + "[XXX] " + msg + ENDC)

def error(msg):
    print(FAIL + "[XXX] " + msg + ENDC)

def runCmd(cmd, showCmd = False):
    if showCmd:
        info(cmd)
    return os.system(cmd)

def runCmd2(cmd, showCmd = False):
    if showCmd:
        info(cmd)
    f = os.popen(cmd)
    text = f.read()
    f.close()
    return text

def readText(path):
    f = open(path, "r")
    text = f.read()
    f.close
    return text

def writeText(path, text):
    f = open(path, "w")
    f.write(text)
    f.close()

def clone(project_name):
    cmd = "<NAME_EMAIL>:fliggy_mobile/flite.git %s" % project_name
    runCmd(cmd)
    cmd = "rm -rf %s/.git" % project_name
    runCmd(cmd)
    return True

def toCamelName(name, firstLetterCapitalize = False):
    string_list = re.split("[_-]", name) #将字符串转化为list
    first =  string_list[0].lower()
    others = string_list[1:]

    others_capital = [word.capitalize() for word in others]      #str.capitalize():将字符串的首字母转化为大写
    others_capital[0:0] = [first]
    hump_string = ''.join(others_capital)     #将list组合成为字符串，中间无连接符。
    if firstLetterCapitalize:
        hump_string = hump_string[0].upper() + hump_string[1:]
    return hump_string

def reameDartCode(project_name, projectDir):
    pubspecYamlPath = "%s/pubspec.yaml" % projectDir
    pubspecYamlText = readText(pubspecYamlPath)
    pubspecYamlText = re.sub(r"name: flite", "name: %s" % project_name, pubspecYamlText)
    pubspecYamlText = re.sub(r"androidPackage: com.taobao.trip.flite", "androidPackage: com.taobao.trip.%s" % project_name, pubspecYamlText)
    pubspecYamlText = re.sub(r"pluginClass: FlitePlugin", "pluginClass: %sPlugin" % toCamelName(project_name, True), pubspecYamlText)
    # print pubspecYamlText
    writeText(pubspecYamlPath, pubspecYamlText)

    oldfliteDartPath = "%s/lib/flite.dart" % projectDir
    fliteDartPath = "%s/lib/%s.dart" % (projectDir, project_name)
    runCmd("mv %s %s" % (oldfliteDartPath, fliteDartPath))

    fliteDartText = readText(fliteDartPath)
    fliteDartText = re.sub(r"import 'package:flite/page/example_page.dart';", "", fliteDartText)
    fliteDartText = re.sub(r"ExamplePage\(params: params\)", "Container()", fliteDartText)
    fliteDartText = re.sub(r"String package = 'flite'", "String package = '%s'" % project_name, fliteDartText)
    fliteDartText = re.sub(r"String package = 'flite'", "String package = '%s'" % project_name, fliteDartText)
    fliteDartText = re.sub(r"FliteModule", "%sModule" % toCamelName(project_name, True), fliteDartText)
    fliteDartText = re.sub(r"package:flite", "package:%s" % project_name, fliteDartText)
    writeText(fliteDartPath, fliteDartText)

    examplePubspecYamlPath = "%s/example/pubspec.yaml" % projectDir
    examplePubspecYamlText = readText(examplePubspecYamlPath)
    examplePubspecYamlText = re.sub(r"flite:", "%s:" % project_name, examplePubspecYamlText)
    writeText(examplePubspecYamlPath, examplePubspecYamlText)

    exampleMainDartPath = "%s/example/lib/main.dart" % projectDir
    exampleMainDartText = readText(exampleMainDartPath)
    exampleMainDartText = re.sub(r"import 'package:flite/flite.dart';", "import 'package:%s/%s.dart';" % (project_name, project_name), exampleMainDartText)
    exampleMainDartText = re.sub(r"FliteModule", "%sModule" % toCamelName(project_name, True), exampleMainDartText)
    writeText(exampleMainDartPath, exampleMainDartText)

    testPath = "%s/test/flite_test.dart" % projectDir
    testText = readText(testPath)
    testText = re.sub(r"flite", "%s" % project_name, testText)
    writeText(testPath, testText)

def renameJavaPlugin(project_name, projectDir):
    cmd = "mv %s/android/src/main/java/com/taobao/trip/flite %s/android/src/main/java/com/taobao/trip/%s" % (projectDir, projectDir, project_name)
    runCmd(cmd)

    oldJavaPluginPath = "%s/android/src/main/java/com/taobao/trip/%s/FlitePlugin.java" % (projectDir, project_name)
    newJavaPluginPath = "%s/android/src/main/java/com/taobao/trip/%s/%sPlugin.java" % (projectDir, project_name, toCamelName(project_name, True))
    runCmd("mv %s %s" % (oldJavaPluginPath, newJavaPluginPath))

    javaPluginText = readText(newJavaPluginPath)
    javaPluginText = re.sub(r"package com.taobao.trip.flite;", "package com.taobao.trip.%s;" % project_name, javaPluginText)
    javaPluginText = re.sub(r"FlitePlugin", "%sPlugin" % toCamelName(project_name, True), javaPluginText)
    javaPluginText = re.sub(r"\"flite\"", "\"%s\"" % project_name, javaPluginText)
    writeText(newJavaPluginPath, javaPluginText)

    javaAndroidManifestPath = "%s/android/src/main/AndroidManifest.xml" % (projectDir)
    javaAndroidManifestText = readText(javaAndroidManifestPath)
    javaAndroidManifestText = re.sub(r"com.taobao.trip.flite", "com.taobao.trip.%s" % project_name, javaAndroidManifestText)
    writeText(javaAndroidManifestPath, javaAndroidManifestText)

    settingGradlePath = "%s/android/settings.gradle" % (projectDir)
    settingGradleText = readText(settingGradlePath)
    settingGradleText = re.sub(r"'flite'", "'%s'" % project_name, settingGradleText)
    writeText(settingGradlePath, settingGradleText)

def renameIosPlugin(project_name, projectDir):
    oldPodspecPath = "%s/ios/flite.podspec" % projectDir
    newPodspecPath = "%s/ios/%s.podspec" % (projectDir, project_name)
    runCmd("mv %s %s" % (oldPodspecPath, newPodspecPath))

    podspecText = readText(newPodspecPath)
    podspecText = re.sub(r"s.name             = 'flite'", "s.name             = '%s'" % project_name, podspecText)
    # print pubspecYamlText
    writeText(newPodspecPath, podspecText)

    oldPluginHPath = "%s/ios/Classes/FlitePlugin.h" % projectDir
    newPluginHPath = "%s/ios/Classes/%sPlugin.h" % (projectDir, toCamelName(project_name, True))
    runCmd("mv %s %s" % (oldPluginHPath, newPluginHPath))

    oldPluginMPath = "%s/ios/Classes/FlitePlugin.m" % projectDir
    newPluginMPath = "%s/ios/Classes/%sPlugin.m" % (projectDir, toCamelName(project_name, True))
    runCmd("mv %s %s" % (oldPluginMPath, newPluginMPath))

    pluginHText = readText(newPluginHPath)
    pluginHText = re.sub(r"FlitePlugin", "%sPlugin" % toCamelName(project_name, True), pluginHText)
    writeText(newPluginHPath, pluginHText)

    pluginMText = readText(newPluginMPath)
    pluginMText = re.sub(r"FlitePlugin", "%sPlugin" % toCamelName(project_name, True), pluginMText)
    pluginMText = re.sub(r"@\"flite\"", "@\"%s\"" % project_name, pluginMText)
    writeText(newPluginMPath, pluginMText)


def renameAionShell(project_name, projectDir):
    pubspecYamlPath = "%s/aion_shell/pubspec.yaml" % projectDir
    pubspecYamlText = readText(pubspecYamlPath)
    pubspecYamlText = re.sub(r"flite", "%s" % project_name, pubspecYamlText)
    pubspecYamlText = re.sub(r"flite", "%s" % project_name, pubspecYamlText)
    writeText(pubspecYamlPath, pubspecYamlText)

    mainPath = "%s/aion_shell/lib/main.dart" % projectDir
    mainText = readText(mainPath)
    mainText = re.sub(r"import 'package:flite/flite.dart';", "import 'package:%s.dart';" % (project_name + "/" + project_name), mainText)
    writeText(mainPath, mainText)

def renameBuildShell(projectName, projectDir):
    pubspecYamlPath = "%s/build_shell/pubspec.yaml" % projectDir
    pubspecYamlText = readText(pubspecYamlPath)
    pubspecYamlText = re.sub(r"flite", "%s" % projectName, pubspecYamlText)
    pubspecYamlText = re.sub(r"flite", "%s" % projectName, pubspecYamlText)
    writeText(pubspecYamlPath, pubspecYamlText)

    build_shellDartPath = "%s/build_shell/lib/build_shell.dart" % projectDir
    build_shellDartText = readText(build_shellDartPath)
    build_shellDartText = re.sub(r"flite", "%s" % projectName, build_shellDartText)
    build_shellDartText = re.sub(r"flite", "%s" % projectName, build_shellDartText)
    writeText(build_shellDartPath, build_shellDartText)

def rename(project_name):
    projectDir = "%s/%s" % (os.getcwd(), project_name)
    # print projectDir
    reameDartCode(project_name, projectDir)
    renameJavaPlugin(project_name, projectDir)
    renameIosPlugin(project_name, projectDir)
    renameAionShell(project_name, projectDir)
    renameBuildShell(project_name, projectDir)

    runCmd("cd %s/aion_shell/ && flutter pub get" % project_name)
    runCmd("cd %s/build_shell/ && flutter pub get" % project_name)
    runCmd("cd %s/ && flutter pub get" % project_name)

def processIOSEnv(proj_path):
    pod_location = subprocess.run(['which', 'pod'], stdout=subprocess.PIPE).stdout.decode('utf-8').replace('\n', '')
    tpod_location = subprocess.run(['which', 'tpod'], stdout=subprocess.PIPE).stdout.decode('utf-8').replace('\n', '')
    info("pod:%s tpod:%s"%(pod_location, tpod_location))
    
    if len(tpod_location) < 0:
        error("请安装tpod: sh <(curl http://tpm.taobao.net/taobaoSetup.sh)")
        return False
    else:
        info('检测到系统已经安装tpod(%s)'%(tpod_location))

    if len(pod_location) > 0 and pod_location.find('tbt') <= 0:
        info("删除本地pod, 目录:%s"%(pod_location))
        try:
            os.remove(os.path.dirname(pod_location))
        except PermissionError:
            shutil.os.system('sudo rm -rf "{}"'.format(pod_location))
        
    script_path = os.path.abspath(os.path.join(proj_path, "./scripts/pod"))
    dest_path = os.path.abspath(os.path.join(tpod_location, '../', 'pod'))
    info("copy %s to %s"%(script_path, dest_path))
    shutil.copy(script_path, dest_path)
    shutil.os.system('chmod +x  "{}"'.format(dest_path))

    return True

def checkEnv():
    return True
    # cmd = "flutter --version|grep Flutter"
    # versionStr = runCmd2(cmd)
    # match = re.search(r"Flutter (\d+\.\d+\.\d+)", versionStr, re.I)
    # if match:
    #     version = match.group(1)
    #     if version == "1.12.13":
    #         return True
    #     else:
    #         error("目前项目只能运行在1.12.13版本，请先切换flutter分支到v1.12.13-hotfixes, 或执行以下命令\nflutter channel v1.12.13-hotfixes")
    #         return False
    # error("请先安装flutter sdk，并正确配置环境！ https://flutter.dev/docs/get-started/install/macos")
    # return False

if __name__ == '__main__':
    if checkEnv():
        info("请输入您要创建的项目名: ")
        project_name = input()
        projectDir = "%s/%s" % (os.getcwd(), project_name)
        if not os.path.exists(projectDir):
            # print project_name
            if clone(project_name):                
                rename(project_name)
                if processIOSEnv(projectDir):
                    info("项目创建完成，请用Android Studio打开目录 %s" % projectDir)
                    exit(0)
            error('创建目录失败')
        else:
            error("%s 目录己存在, 请重新输入一个名字！" % projectDir)
