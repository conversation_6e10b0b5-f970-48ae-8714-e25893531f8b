#import "FlutterTravelMemoriesPhotoAlbumPlugin.h"

#import <FliggyCommonUI/FliggyPhotoHelper.h>

#import <FliggyUtility/KVCache.h>

#import <FliggyUtility/NSDictionary+JSON.h>

#import <YYModel/YYModel.h>

// 持有协议
@interface FlutterTravelMemoriesPhotoAlbumPlugin() <FliggyPhotoHelperDelegate>

@property (nonatomic,strong) FliggyPhotoHelper *FlutterphotoHelper;
// 全局结果
@property(nonatomic, copy)FlutterResult result;

@end

@implementation FlutterTravelMemoriesPhotoAlbumPlugin

+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {

  // methodchannel是一次性的
  FlutterMethodChannel* channel = [FlutterMethodChannel
      methodChannelWithName:@"flutter_travel_memories_photo_album"
            binaryMessenger:[registrar messenger]];
  FlutterTravelMemoriesPhotoAlbumPlugin* instance = [[FlutterTravelMemoriesPhotoAlbumPlugin alloc] init];
  [registrar addMethodCallDelegate:instance channel:channel];
}

- (instancetype)init{
    if (self = [super init]) {
        // 这个地方必须初始化，否则后面的执行不了
        self.FlutterphotoHelper = [[FliggyPhotoHelper alloc] init];
        self.FlutterphotoHelper.delegate = self;
    }
    return self;
}

- (void)handleMethodCall:(FlutterMethodCall*)call result:(FlutterResult)result {
    self.result = result;
    // 使用eventchannel发送消息给flutter
    [[NSNotificationCenter defaultCenter] postNotificationName:@"channeltest" object:nil];

    // flutter发送消息给iOS
   if ([@"getPlatformVersion" isEqualToString:call.method]) {
     result([@"iOS " stringByAppendingString:[[UIDevice currentDevice] systemVersion]]);
    // result("version");
   } else if([@"getPhotoCache" isEqualToString:call.method]){
    [self getPhotoCache:call result:result];
   } else if ([@"uploadPic" isEqualToString:call.method]) {
    [self uploadPic:call result:result];
   } else if ([@"checkResult" isEqualToString:call.method]) {
    [self checkResult:call result:result];
   } else if ([@"save2KVCache" isEqualToString:call.method]) {
    // 存kvcache
    [self save2KVCache:call result:result];
   } else if ([@"getPhotoPath" isEqualToString:call.method]) {
       [self getPhotoPath:call result:result];
   } else {
       result(FlutterMethodNotImplemented);
   }
}

- (void)getPhotoCache:(FlutterMethodCall *)call result:(FlutterResult)result {
    NSString *key = call.arguments;
    NSString *jsonString = [[KVCache getInstance] execute:KVCache_GET args:@[key]];
    // mock数据，提交的时候不要上
    // NSString *jsonString = @"{\"precision\":3,\"65.682,17.548\":\"索舍勒\",\"37.760,-122.510\":\"奥克兰\"}";
    NSDictionary *locCityDic = [NSDictionary dictionaryWithJsonString:jsonString];
    result(jsonString);
}

- (void)uploadPic:(FlutterMethodCall *)call result:(FlutterResult)result{
        // 取出参数
        NSDictionary *params = call.arguments;
        // 定义成局部变量，在执行后会被销毁，需要定义成全局变量
        // FliggyPhotoHelper *photoHelper = [[FliggyPhotoHelper alloc] init];
//    FlutterphotoHelper = [[FliggyPhotoHelper alloc] init];
        [self.FlutterphotoHelper uploadPic:params callback:^(NSDictionary *picInfos){
            if (picInfos == nil ) {
                result(@{@"result":@"failed"});
            } else {
                [picInfos setValue:@"success" forKey:@"result"];

                NSData  *jsonData = [NSJSONSerialization dataWithJSONObject:picInfos options:NSJSONWritingPrettyPrinted error:nil];
                NSString *jsonStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
                result(jsonStr);
            }
            printf(@"picInfos:%@",picInfos);
        }];
}

- (void)checkResult:(FlutterMethodCall *)call result:(FlutterResult)result {
    NSString *jsonString = [[KVCache getInstance] execute:KVCache_GET args:@[@"photoUploadResult"]];
    NSDictionary *locCityDic = [NSDictionary dictionaryWithJsonString:jsonString];
    // 如果缓存里没有数据，就返回错误
    // native端控制是否成功，不成功就不写缓存
    if (locCityDic == nil) {
        result(@{@"result":@"failed"});
    } else {
        // 如果缓存里有数据，返回成功
        result(@{@"result":@"success"});
    }
}

- (void)resultCallBack:(id) result{
    if (result == nil) {
        NSLog(@"resultCallBack:%@",result);
        NSDictionary *dic = [NSDictionary dictionaryWithValuesForKeys:@[@"error",@"result",@"failed",@"message"]];
        self.result(dic);
        return;
    }
    NSString* jsonInfo = [result yy_modelToJSONString];
    NSLog(@"resultCallBack:%@",jsonInfo);
    NSDictionary *dic = [NSDictionary dictionaryWithValuesForKeys:@[@"error",@"result",jsonInfo,@"message"]];
    self.result(dic);
}

- (void)save2KVCache:(FlutterMethodCall *)call result:(FlutterResult)result{
    // 这里不能直接用map承接，获取到的是string
    NSString *jsonString = call.arguments;
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *jsonDict = [NSJSONSerialization JSONObjectWithData:jsonData options:NSJSONReadingMutableContainers error:nil];
    
    [[KVCache getInstance] execute:KVCache_SET args:@[jsonDict[@"key"],jsonDict[@"value"]]];
}

-(FliggyPhotoHelper *)FliggyPhotoHelper{
    if(!_FlutterphotoHelper){
        _FlutterphotoHelper = [[FliggyPhotoHelper alloc] init];
    }
    return _FlutterphotoHelper;
}

-(void)getPhotoPath:(FlutterMethodCall *)call result:(FlutterResult)result {
    
    NSArray *array = [NSArray arrayWithObject:call.arguments];
    PHFetchResult *fetchResult = [PHAsset fetchAssetsWithLocalIdentifiers:array options:nil];
    PHAsset *asset = fetchResult.firstObject;
    
    PHContentEditingInputRequestOptions* options = [[PHContentEditingInputRequestOptions alloc] init];
    options.canHandleAdjustmentData = ^(PHAdjustmentData *adjustmentData) {
        return YES;
    };
    
    [asset requestContentEditingInputWithOptions:options completionHandler:^(PHContentEditingInput *contentEditingInput, NSDictionary *info) {
        NSURL *imageURL = contentEditingInput.fullSizeImageURL;
        NSString *path = [imageURL path];
        NSLog(@"Image path: %@", path);
        result(path);
    }];
}
@end
