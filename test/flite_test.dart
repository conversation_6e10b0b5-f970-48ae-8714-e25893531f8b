import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_travel_memories_photo_album/flutter_travel_memories_photo_album.dart';

void main() {
  const MethodChannel channel = MethodChannel('flutter_travel_memories_photo_album');

  TestWidgetsFlutterBinding.ensureInitialized();

  setUp(() {
    channel.setMockMethodCallHandler((MethodCall methodCall) async {
      return '42';
    });
  });

  tearDown(() {
    channel.setMockMethodCallHandler(null);
  });

  test('getPlatformVersion', () async {
//    expect(await Flutterbiz.platformVersion, '42');
  });
}
