# -*- coding: utf-8 -*-
import os, re, sys, time, json, shutil, urllib, tempfile, argparse, subprocess

HEADER = '\033[95m'
OKBLUE = '\033[94m'
OKGREEN = '\033[92m'
WARNING = '\033[93m'
FAIL = '\033[91m'
ENDC = '\033[0m'
BOLD = "\033[1m"


def infog(msg):
    print(OKGREEN + "[XXX] " + msg + ENDC)


def info(msg):
    print(OKBLUE + "[XXX] " + msg + ENDC)


def warn(msg):
    print(WARNING + "[XXX] " + msg + ENDC)


def error(msg):
    print(FAIL + "[XXX] " + msg + ENDC)


def runCmd(cmd, showCmd=False):
    if showCmd:
        info(cmd)
    return os.system(cmd)


def runCmd2(cmd, showCmd=False):
    if showCmd:
        info(cmd)
    f = os.popen(cmd)
    text = f.read()
    f.close()
    return text


def readText(path):
    f = open(path, "r")
    text = f.read()
    f.close
    return text


def writeText(path, text):
    f = open(path, "w")
    f.write(text)
    f.close()


def toCamelName(name, firstLetterCapitalize=False):
    string_list = re.split("[_-]", name)  # 将字符串转化为list
    first = string_list[0].lower()
    others = string_list[1:]

    others_capital = [word.capitalize() for word in others]  # str.capitalize():将字符串的首字母转化为大写
    others_capital[0:0] = [first]
    hump_string = ''.join(others_capital)  # 将list组合成为字符串，中间无连接符。
    if firstLetterCapitalize:
        hump_string = hump_string[0].upper() + hump_string[1:]
    return hump_string


def checkEnv():
    return True


def clone(name):
    cmd = "<NAME_EMAIL>:fliggy_mobile/flite.git %s" % name
    runCmd(cmd)
    cmd = "rm -rf %s/.git" % name
    runCmd(cmd)
    return True


def rebuildAionShell(projectDir, project_name):
    pubspecYamlPath = "%s/aion_shell/pubspec.yaml" % projectDir
    pubspecYamlText = readText(pubspecYamlPath)
    pubspecYamlText = re.sub(r"flite", "%s" % project_name, pubspecYamlText)
    pubspecYamlText = re.sub(r"flite", "%s" % project_name, pubspecYamlText)
    writeText(pubspecYamlPath, pubspecYamlText)

    mainPath = "%s/aion_shell/lib/main.dart" % projectDir
    mainText = readText(mainPath)
    mainText = re.sub(r"import 'package:flite/flite.dart';",
                      "import 'package:%s.dart';" % (project_name + "/" + project_name), mainText)
    writeText(mainPath, mainText)


def rebuildProject(projectDir, projectName):
    shutil.move(projectDir + "/temp/aion.sh", projectDir + "/aion.sh")

    shutil.move(projectDir + "/temp/aion_shell", projectDir + "/aion_shell")
    rebuildAionShell(projectDir, projectName)

    os.rename(projectDir + "/lib/%s.dart" % projectName, projectDir + "/lib/%s_old.dart" % projectName)
    shutil.move(projectDir + "/temp/lib/flite.dart", projectDir + "/lib/%s.dart" % projectName)
    fliteDartPath = projectDir + "/lib/%s.dart" % projectName
    fliteDartText = readText(fliteDartPath)
    fliteDartText = re.sub(r"import 'package:flite/page/example_page.dart';", "", fliteDartText)
    fliteDartText = re.sub(r"ExamplePage\(params: params\)", "Container()", fliteDartText)
    fliteDartText = re.sub(r"String package = 'flite'", "String package = '%s'" % projectName, fliteDartText)
    fliteDartText = re.sub(r"FliteModule", "%sModule" % toCamelName(projectName, True),
                           fliteDartText)
    fliteDartText = re.sub(r"package:flite", "package:%s" % projectName, fliteDartText)
    writeText(fliteDartPath, fliteDartText)

    pubspecYamlPath = "%s/pubspec.yaml" % projectDir
    pubspecYamlText = readText(pubspecYamlPath)
    if pubspecYamlText.find("aion_sdk") <= 0:
        pubspecYamlText = re.sub(r"  flutter:\n    sdk: flutter", "  flutter:\n    sdk: flutter\n  aion_sdk:\n     sdk: flutter", pubspecYamlText)
        writeText(pubspecYamlPath, pubspecYamlText)

def rebuildBuildShell(projectDir, projectName):
    shutil.move(projectDir + "/temp/build_shell", projectDir + "/build_shell")
    pubspecYamlPath = "%s/build_shell/pubspec.yaml" % projectDir
    pubspecYamlText = readText(pubspecYamlPath)
    pubspecYamlText = re.sub(r"flite", "%s" % projectName, pubspecYamlText)
    pubspecYamlText = re.sub(r"flite", "%s" % projectName, pubspecYamlText)
    writeText(pubspecYamlPath, pubspecYamlText)

    build_shellDartPath = "%s/build_shell/lib/build_shell.dart" % projectDir
    build_shellDartText = readText(build_shellDartPath)
    build_shellDartText = re.sub(r"flite", "%s" % projectName, build_shellDartText)
    build_shellDartText = re.sub(r"flite", "%s" % projectName, build_shellDartText)
    writeText(build_shellDartPath, build_shellDartText)

    runCmd("cd build_shell/ && python3 syncyaml.py")

def refreshProject(projectDir):
    shutil.rmtree("temp")
    runCmd("flutter pub get")
    runCmd("cd aion_shell/ && flutter pub get")
    runCmd("cd build_shell/ && flutter pub get")
    runCmd("git add .")


if __name__ == '__main__':
    if checkEnv():
        projectDir = os.getcwd()
        split = projectDir.split('/')
        projectName = split[len(split) - 1]
        print("projectDir=%s" % projectDir)
        print("projectName=%s" % projectName)
        if not os.path.exists(projectDir + "/aion_shell") or not os.path.exists(projectDir + "/build_shell"):
            if clone("temp"):
                if not os.path.exists(projectDir + "/aion_shell"):
                        rebuildProject(projectDir, projectName)
                if not os.path.exists(projectDir + "/build_shell"):
                        rebuildBuildShell(projectDir, projectName)
                refreshProject(projectDir)
            info("祝贺！项目 [Aion] 化完成 🎉🎉🎉。接下来你需要把 /%s_old.dart 中的内容根据你的逻辑合并到 /%s.dart 中，然后再删除 /%s_old.dar .." % (projectName, projectName, projectName))
        else:
            infog("恭喜！该项目已经支持 [Aion] 了🎉🎉🎉")
