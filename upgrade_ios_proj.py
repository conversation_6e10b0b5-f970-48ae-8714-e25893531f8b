# -*- coding: utf-8 -*-

import tempfile
import sys
import os
import re
import shutil
import subprocess

HEADER = '\033[95m'
OKBLUE = '\033[94m'
OKGREEN = '\033[92m'
WARNING = '\033[93m'
FAIL = '\033[91m'
ENDC = '\033[0m'
BOLD = "\033[1m"

def infog(msg):
    print(OKGREEN + "[XXX] " + msg + ENDC)

def info(msg):
    print(OKBLUE + "[XXX] " + msg + ENDC)

def warn(msg):
    print(WARNING + "[XXX] " + msg + ENDC)

def error(msg):
    print(FAIL + "[XXX] " + msg + ENDC)

def runCmd(cmd, showCmd = False):
    if showCmd:
        info(cmd)
    return os.system(cmd)

def runCmd2(cmd, showCmd = False):
    if showCmd:
        info(cmd)
    f = os.popen(cmd)
    text = f.read()
    f.close()
    return text

def clone(project_name):
    cmd = "<NAME_EMAIL>:fliggy_mobile/flite.git %s" % project_name
    runCmd(cmd)
    cmd = "rm -rf %s/.git" % project_name
    runCmd(cmd)
    return True

def processIOSEnv():
    proj_path = './'
    pod_location = subprocess.run(['which', 'pod'], stdout=subprocess.PIPE).stdout.decode('utf-8').replace('\n', '')
    tpod_location = subprocess.run(['which', 'tpod'], stdout=subprocess.PIPE).stdout.decode('utf-8').replace('\n', '')
    info("pod:%s tpod:%s"%(pod_location, tpod_location))
    
    if len(tpod_location) < 0:
        error("请安装tpod: sh <(curl http://tpm.taobao.net/taobaoSetup.sh)")
        return False
    else:
        info('检测到系统已经安装tpod(%s)'%(tpod_location))

    if len(pod_location) > 0 and pod_location.find('tbt') <= 0:
        info("删除本地pod, 目录:%s"%(pod_location))
        try:
            os.remove(os.path.dirname(pod_location))
        except PermissionError:
            shutil.os.system('sudo rm -rf "{}"'.format(pod_location))
        
    script_path = os.path.abspath(os.path.join(proj_path, "./scripts/pod"))
    dest_path = os.path.abspath(os.path.join(tpod_location, '../', 'pod'))
    info("copy %s to %s"%(script_path, dest_path))
    shutil.copy(script_path, dest_path)
    shutil.os.system('chmod +x  "{}"'.format(dest_path))

    return True    

if __name__ == '__main__':
    if os.path.isdir('./example'):
        tmp_path = tempfile.mktemp(dir=r"/tmp/")
        info("拉取flit代码到%s"%(tmp_path))
        clone(tmp_path)
        runCmd('cp -rf %s/scripts ./'%(tmp_path))
        runCmd('cp -rf %s/example/ios ./example/'%(tmp_path))
        runCmd('rm -rf %s'%(tmp_path))

        processIOSEnv()
    else:
        error('请在项目跟目录下运行(python ./build_shell/upgrade_ios_proj.py)')


