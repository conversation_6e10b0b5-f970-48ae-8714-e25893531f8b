
import 'package:flutter_common/flutter_common.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/pages/home/<USER>';
import 'package:flutter_travel_memories_photo_album/pages/map_photo_album_page/map_photo_album_list_page.dart';
import 'package:flutter_travel_memories_photo_album/pages/map_photo_album_page/map_photo_page.dart';
import 'package:flutter_travel_memories_photo_album/pages/preview/one_photo_preview_page.dart';
import 'package:flutter_travel_memories_photo_album/pages/preview/photo_preview_page.dart';

/// 【非常重要】！！！
/// 使用图片，必须要加上该 package 参数，否则在动态化中将无法加载到图片
/// 如：Image.asset("assets/images/bg.png", package: package,),
String package = 'flutter_travel_memories_photo_album';

/// 动态包版本
String bundleVersion = '0.0.0';


/// 页面在此处配置<br>
/// 页面名称规范：<业务>/<页面>，如 /fhotel/media_list
/// <br>必须遵从规范，否则无法使用 aion 离线包，以及进行内部页面跳转
Map<String, FliggyPageBuilder> pages = {
  '/flutter_picture_picker_home': (pageName, params, _) => FliggyPhotoSelectHomePage(params: params,),
  '/flutter_picture_picker_home/home': (pageName, params, _) => FliggyPhotoSelectHomePage(params: params,),
  '/flutter_picture_picker_map/photo': (pageName, params, _) => FliggyMapPhotoPage(params: params),
  '/flutter_picture_picker/preview': (pageName, params, _) => FliggyPhotoPreviewPage(),
  '/flutter_picture_picker_map_list/photo': (pageName, params, _) => FliggyMapPhotoAlbumListPage(),
  '/flutter_picture_picker/onePreview': (pageName, params, _) => FliggyPhotoOnePreviewPage(params: params),
};

/// 由于 AionBundle 中，所有的自定义对象都是全新的独立对象（FlutterBoost 除外），因此宿主工程中的变量及状态与 AionBundle 中的都是不同的。<br>
/// Aion 编译出来的 Bundle 产物，所有的自定义类（即非 Flutter SDK 中的类）都会增加 _kbc 后缀变成新的类，因此即使是同一段代码，也与宿主工程完全不同了。<br>
/// 开发者应该根据需要，进行自己的初始化。<br>
/// 如在需要 [DXEngine] 的 Bundle 中，开发者应该在此处进行 [DXEngine] 的初始化。<br>
/// 【⚠️注意】：多余的初始化会引入无意义的代码到产物中，从而导致产物体积增加
void init() {
  // todo 通过 Aion 加载该 Bundle 时，进行 Bundle 的初始化
}

class FlutterTravelMemoriesPhotoAlbumModule with FliggyModule {
  FlutterTravelMemoriesPhotoAlbumModule() {
    biz = package;
    pageNames = pages.keys.toList();
  }
  @override
  void onPageRegister() {
    // 注册入口
    // FliggyNavigatorApi.getInstance().registerPageBuilders({
    //   AlbumRouter.homeRouter: (pageName, params, _) => FliggyPhotoSelectHomePage(params: params),
    // });
    FliggyNavigatorApi.getInstance().registerPageBuilders(pages);
  }

  @override
  void onModuleInit() {}
}
