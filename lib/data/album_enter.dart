

import 'package:flutter/material.dart';

/// 业务数据处理隔离层
///

class FliggyPicturePicker extends StatefulWidget {
  const FliggyPicturePicker({Key? key}) : super(key: key);

  @override
  State<FliggyPicturePicker> createState() => _FliggyPicturePickerState();
}

class _FliggyPicturePickerState extends State<FliggyPicturePicker> {
  @override
  Widget build(BuildContext context) {
    return Container();
  }
}




