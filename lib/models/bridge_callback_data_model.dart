
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';



/// 网络回调模型，最后传回去的应该是json编码过的

class BridgeCallbackDataModel{
  // url链接
  List<String> imgUrls = [];
  List<DetailModel> detailArray = [];

  // 这里直接接受native回传回来的网络数据就好了
  static BridgeCallbackDataModel dataToModel (BuildContext context,List urlList,List<FliggyPictureModel> selectPictures) {
    BridgeCallbackDataModel callbackDataModel = new BridgeCallbackDataModel();
    // 如果给到的链接和选择的照片数量不一致
    if (urlList.length != selectPictures.length){
      FliggyUserTrackApi.getInstance().custom(context, "uploadPicFailedSpm",
          "", {"uploadnum":selectPictures.length.toString(),"urlnum":urlList.length.toString()});
      return callbackDataModel;
    } else {
      Map<String,String> spmMap = {};
      for ( int i = 0; i<urlList.length; i++){
        dynamic url = urlList[i];
        DetailModel detailModel = new DetailModel();
        detailModel = detailModel.dataToModel(url,selectPictures[i],needTime: true);
        detailModel.index = i;
        callbackDataModel.imgUrls.add(url);
        if (Platform.isIOS) {
          spmMap[selectPictures[i].id] = url.toString();
        } else {
          spmMap[selectPictures[i].path] = url.toString();
        }
        // 添加
        callbackDataModel.detailArray.add(detailModel);
      }
    }
    return callbackDataModel;
  }

  // 模型转json
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['imgUrls'] = this.imgUrls;
    data['detailArray'] = this.detailArray.map((v) => v.toJson()).toList();
    return data;
  }

}

class DetailModel {
  // 序号
  late int index;
  // 时间，格式：yyyy-mm-dd
  late String time;
  // 时间，格式：yyyy:mm:dd hh:mm:ss
  late String timeDate;
  // 驱动来源 枚举项：Apple，？？
  late String deviceMake;
  // 上传链接
  late String resourceURL;
  // 经纬度，不四舍五入的
  late String lng;
  late String lat;
  // ???
  late String deviceFNumber;
  late String deviceModel;
  late String deviceFocalLength;

  DetailModel dataToModel(String url, FliggyPictureModel selectPicture,{bool? needTime}) {
    DetailModel detailModel = new DetailModel();
    // 资源链接
    detailModel.resourceURL = url;
    // 经纬度信息
    if (selectPicture.longitude != null) {
      detailModel.lng = selectPicture.longitude.toString();
    }
    if (selectPicture.latitude != null) {
      detailModel.lat = selectPicture.latitude.toString();
    }
    // 时间
    if (needTime != null && needTime) {
      if (selectPicture.createTime.length == 13) {
        detailModel.time = DateFormat('yyyy:MM:dd HH:mm:ss').format(DateTime.fromMillisecondsSinceEpoch(int.parse(selectPicture.createTime)));
      } else {
        detailModel.time = selectPicture.createTime.toString();
      }
    }


    return detailModel;

  }

  // 模型转json
  Map<String, dynamic> toJson() {
    DetailModel instance = this;
    Map <String, dynamic> jsMap = {};
    if (instance.index != null) {
      jsMap['index'] = instance.index;
    }
    if (instance.time.isNotEmpty) {
      jsMap['time'] = instance.index;
    }
    if (instance.resourceURL != null) {
      jsMap['resourceURL'] = instance.resourceURL;
    }
    if (instance.lng != null) {
      jsMap['lng'] = instance.lng;
    }
    if (instance.lat != null) {
      jsMap['lat'] = instance.lat;
    }
    return jsMap;
  }

}
// picture数据参考
//{
// "id": "9096",
// "innerId": "9096",
// "title": "Screenshot_2022-10-19-10-14-47-887_com.taobao.trip.lite.jpg",
// "path: /storage/emulated/0/DCIM/Screenshots/Screenshot_2022-10-19-10-14-47-887_com.taobao.trip.lite.jpg",
// "albumName": "Recent",
// "albumId": "isAll",
// "latitude": "0.0",
// "longitude": "0.0",
// "createTime": "2022-10-19 10:14:47.000",
// "modifiedTime": "2022-10-19 10:14:47.000"
// }