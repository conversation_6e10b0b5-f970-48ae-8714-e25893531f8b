
import 'dart:typed_data';

import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';

class FliggyCityAlbumModel{
  String location;
  int photonumber;
  String time;
  List<FliggyPictureModel> allPicturesList = [];
  List<Uint8List> thumbnailDatas = [];

  FliggyCityAlbumModel({required this.location,required this.photonumber,required this.time,required this.allPicturesList});

  // static FliggyCityAlbumModel fromDict(Map<String,dynamic> map){
  //   FliggyCityAlbumModel model = FliggyCityAlbumModel (
  //       location: map["loaction"],
  //       photonumber: map["photonumber"],
  //       time: map["time"],
  //       albumList: map["albumList"]
  //   );
  //   return model;
  // }

}