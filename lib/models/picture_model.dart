

import 'package:flutter_travel_memories_photo_album/util/album_data_safe.dart';

class FliggyPictureModel {

  /// 图片在相册中的 id
  late String id;
  /// 图片路径
  late String path;
  /// 图片创建时间
  late String createTime;

  double latitude = 0.0;

  double longitude = 0.0;

  late String name;

  // Picture(Map photo):id = photo["id"],path = photo['path'],createTime = photo["createTime"],assert(photo.containsKey("lat"));

  FliggyPictureModel Map2Model(Map photo) {
    FliggyPictureModel pic = new FliggyPictureModel();
    pic.id          = FliggyAlbumDataSafe.safeMapWithDefault(map: photo,key: 'id',defaultValue: '');
    pic.path        = FliggyAlbumDataSafe.safeMapWithDefault(map: photo,key: 'path',defaultValue: '');
    pic.createTime  = FliggyAlbumDataSafe.safeMapWithDefault(map: photo,key: 'createTime',defaultValue: DateTime.now().toString());
    if (photo['lat'] is String) {
      pic.latitude    = double.parse(FliggyAlbumDataSafe.safeMapWithDefault(map: photo,key: 'lat',defaultValue: '0.0'));
    } else if (photo['lat'] is double) {
      pic.latitude    = FliggyAlbumDataSafe.safeMapWithDefault(map: photo,key: 'lat',defaultValue: 0.0);
    } else if (!photo.containsKey('lat')) {
      pic.latitude    = 0.0;
    }
    if (photo['lng'] is String) {
      pic.longitude   = double.parse(FliggyAlbumDataSafe.safeMapWithDefault(map: photo,key: 'lng',defaultValue: '0.0'));
    } else if (photo['lng'] is double) {
      pic.longitude   = FliggyAlbumDataSafe.safeMapWithDefault(map: photo,key: 'lng',defaultValue: 0.0);
    } else if (!photo.containsKey('lng')) {
      pic.longitude   = 0.0;
    }


    pic.name        = FliggyAlbumDataSafe.safeMapWithDefault(map: photo,key: 'name',defaultValue: '');
    return pic;
  }

  static Map Model2Map(FliggyPictureModel model) {

    FliggyPictureModel pic = new FliggyPictureModel();
    Map map = {};
    map['id'] = model.id;
    map['path'] = model.path;
    map['createTime'] = model.createTime;
    map['lat'] = model.latitude;
    map['lng'] = model.longitude;
    map['name'] = model.name;

    return map;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is FliggyPictureModel && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode {
    return id.hashCode;
  }
}

