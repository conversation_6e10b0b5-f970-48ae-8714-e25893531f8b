import 'package:flutter/services.dart';

class AlbumNativeApi {
  /// method通道
  static MethodChannel methodChannel = const MethodChannel('flutter_travel_memories_photo_album');
  /// 上传选择的照片
  static String uploadPic = "uploadPic";
  /// kv存储经纬度城市对应关系
  static String save2KVCache = "save2KVCache";
  /// 获取按城市分类照片缓存
  static String getPhotoCache = "getPhotoCache";
  /// iOS获取单张图片二进制数据
  static String getImageDataForFliggyAlbum = "getImageDataForFliggyAlbum";
  /// 获取相册列表
  static String getPhotosForFliggyAlbum = "getPhotosForFliggyAlbum";
  
  static String getAsignPhotosForFliggyAlbum = "getAsignPhotosForFliggyAlbum";

}