// 定义了事件类型
import 'package:flutter_common/event_bus/event_bus.dart';

// 实例化EventBus库，创建一个事件总线
EventBus eventBus = EventBus();
class BusEvents {

  // 地点信息扫描完成
  static const String EVENT_CITY_UPGRATE_FINISHED = 'cityAlbumUpgrateFinished';
  // 扫描后有城市信息
  static const String HAVE_CITY_DATA = 'havecitydata';
  // 网络接口失败
  static const String NET_ERROR = 'neterror';
  // 扫描后无城市信息
  static const String NO_CITY_DATA = 'havecitydata';
  // 通知单张预览图片
  static const String ONE_PICTURE = 'onePicture';
  // 首页地点信息扫描完成
  static const String HOME_EVENT_CITY_UPGRATE_FINISHED = 'homecityAlbumUpgrateFinished';

  // 通知地点相册页面刷新
  static const String NOTICE_MAP_PHOTO_PAGE_SETSTATE = 'NOTICE_MAP_PHOTO_PAGE_SETSTATE';
  // 在预览页面修改图片选择状态
  static const String EVENT_PREVIEW_SELECT_CHANGE = 'EVENT_PREVIEW_SELECT_CHANGE';
}