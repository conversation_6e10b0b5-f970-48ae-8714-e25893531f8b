class AlbumRouter {
  // 由路由跳转到这些页面
  /// 左下角预览页面
  static String previewRouter = "/flutter_picture_picker/preview";
  /// 首页
  static String homeRouter = "/flutter_picture_picker_home";
  /// 地点相册 图片展示页
  static String mapPhotoRouter = "/flutter_picture_picker_map/photo";
  /// 本地相册 图片展示页
  static String localPhotoRouter = "/flutter_picture_picker_local/photo";
  /// 本地相册 列表页
  static String localPhotoListRouter = "/flutter_picture_picker_local/list";
  /// 地点相册 相册列表页
  static String mapListRouter = "/flutter_picture_picker_map_list/photo";
  /// 单张照片预览页
  static String onePreviewRouter = "/flutter_picture_picker/onePreview";

  // 以下页面可能不是通过路由跳转，定义名称为了上传照片的埋点统一管理使用
  static String allPageRote = "allPageRote";

}