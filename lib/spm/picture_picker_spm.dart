
import 'package:flutter_travel_memories_photo_album/util/album_data_safe.dart';


/// 集团要求 同页面的spm相同，所以这里需要尽量和native的图片选择器的埋点保持一致
///
/// 现有情况：native图片选择器：
///    图片选择器：Page_fliggy_commonui_photopicker：spm:181.8947314.0.0
///    图片选择器中的预览页面：Page_fliggy_commonui_photopicker_browser  spm:181.8947318.0.0
///    我们的图片选择器主要有图片页和列表页，但只是由于结构不同，相册的列表页成了独立的页面，但是认为应该与原来保持一致，他们还是相同的页面
///    基于此，设计了我们新的Flutter相册的埋点结构：除预览页面外，其他页面保持使用spm:181.8947314.0.0，使用page区分不同子页面
///     预览页面的埋点使用原有spm:181.8947318.0.0
class PicturePickerSpm {
  /// 图片选择器b点
  static const photopicker = '181.8947314';
  /// 预览页面b点
  static const browser = '181.8947318';
  /// 权限申请页b点
  static const permission = '181.28261046';

  /************************上传相关埋点***************************/
  /// 图片选择器开始上传图片
  static const String uploadPicStartSpm = "uploadPicStartSpm";

  /// 当需要上传oss的时候，接收回调
  static const String uploadPicOssCallback = "uploadPicOssCallback";

  /// 由图片选择器将图片返回给接口
  static const String uploadPicFinishSpm = "uploadPicFinishSpm";
  /************************页面埋点相关***************************/
  /// 选择航班pageName
  // static const  PageName = 'Page_Picture_Picker';

  /// 进入全部相册列表页 页面埋点
  static String allPicListPage(String spmD) {
    return '$photopicker.allpiclist.${safeString(spmD)}';
  }

  /// 进入全部照片展示页 页面埋点
  static String allPicPage(String spmD) {
    return '$photopicker.allpic.${safeString(spmD)}';
  }

  /// 进入地点相册列表 页面埋点
  static String mapPicListPage(String spmD) {
    return '$photopicker.mappic.${safeString(spmD)}';
  }
  /// 进入地点相册 点击埋点
  static String mapPicPage(String spmD) {
    return '$photopicker.mappic.${safeString(spmD)}';
  }
  /// 进入预览页面 页面埋点
  static String previewPage(String spmD) {
    return '$browser.preview.${safeString(spmD)}';
  }
  /// 进入预览单张页面 页面埋点
  static String previewOnePage(String spmD) {
    return '$browser.previewOne.${safeString(spmD)}';
  }

  /// 进入权限页面 页面埋点，否则后续点击埋点可能错乱
  static String permissionPage(String spmD) {
    return '$permission.previewOne.${safeString(spmD)}';
  }
}
