import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_base_define.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/net/album_request_upload_photo.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_global_config.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/process/upload_pic_process.dart';
import 'package:ftoast/ftoast.dart';
import 'package:fsuper/fsuper.dart';
import 'package:fliggy_router/fliggy_router.dart';

class FliggyPhotoToolView extends StatefulWidget {
  final int level;
  final String pageName;

  FliggyPhotoToolView(Key key, this.level, this.pageName) : super(key: key);

  @override
  FliggyPhotoToolViewState createState() =>
      FliggyPhotoToolViewState(this.level);
}

class FliggyPhotoToolViewState extends State<FliggyPhotoToolView> {
  late int level;

  FliggyPhotoToolViewState(this.level);

  @override
  void initState() {
    // getSystemPhotosThumbnailDatas(pageParams['albumName']);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 375,
      height: 92,
      color: Color(0xFFFFFFFF),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
              padding: EdgeInsets.only(left: 18),
              child: FliggyPhotoAlbumManager().selectPhoto.length > 0
                  ? TextButton(
                      child: Text(
                        "预览",
                        style: TextStyle(
                            color: Color(0xFF000000),
                            fontSize: 15,
                            fontWeight: FontWeight.w600),
                      ),
                      onPressed: () => clickPreview())
                  : TextButton(
                  child: Text(
                    "预览",
                    style: TextStyle(
                        color: Color(0xFF919499),
                        fontSize: 15,
                        fontWeight: FontWeight.w600),
                  ),
                  onPressed: () => null)),
          Spacer(),
          Padding(
              // 位置需要调整
              padding: EdgeInsets.only(right: 20),
              child: FliggyPhotoAlbumManager().selectPhoto.length > 0 ?
              MaterialButton(
                  elevation: 0,
                  height: 35.5,
                  minWidth: 81,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(21),
                  ),
                  color: Color(0xFFFFE033),
                  child: Text(
                    "上传  " +
                        FliggyPhotoAlbumManager().selectPhoto.length.toString(),
                    style: TextStyle(),
                  ),
                  disabledColor: Color(0xFF919499),
                  // 按钮的置灰（不可用状态）要靠这里点击事件为null。。。
                  onPressed: () => UploadPicProcess().clickFinish(context,this.level,widget.pageName)) :
              MaterialButton(
                  elevation: 0,
                  height: 35.5,
                  minWidth: 81,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(21),
                  ),
                  color: Color(0xFFF2F3F5),
                  child: Text(
                    "未选中",
                    style: TextStyle(
                        color: Color(0xFF919499),
                        fontSize: 15,
                        fontWeight: FontWeight.w600),
                  ),
                  onPressed: () => null)
          )
        ],
      ),
    );
  }

  ///点击预览
  clickPreview() {
    if (FliggyPhotoAlbumManager().selectPhoto.isEmpty) return;
    FliggyNavigatorApi.getInstance().push(context, "page://flutter_view/flutter_picture_picker/preview", params: {
      "un_flutter": true,
      "flutter_path": AlbumRouter.previewRouter,
    });
  }


}
