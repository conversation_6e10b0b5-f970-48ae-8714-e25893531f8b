import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:ficonfont/ficonfont.dart' as FIcon;
import 'package:flutter_travel_memories_photo_album/common/album_events.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:fliggy_router/fliggy_router.dart';

class FliggyPhotoView extends StatefulWidget {
  FliggyPhotoView(
      {Key? key, required this.picture, required this.clickCallBack, required this.index, required this.pic,required this.selected});

  // var key;
  final Widget picture;
  final Function clickCallBack;
  final int index;
  final FliggyPictureModel pic;
  bool selected;

  @override
  State<FliggyPhotoView> createState() => FliggyPhotoViewState();
}

class FliggyPhotoViewState extends State<FliggyPhotoView> {

  @override
  void initState() {
    // getSystemPhotosThumbnailDatas(pageParams['albumName']);
  }


  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          /// 上报点击图片，依旧不使用路由格式转化
          jump2OnePreviewPage(widget.pic);
        },
        child: Container(
            clipBehavior: Clip.hardEdge,
            // padding: EdgeInsets.fromLTRB(0, 3, 3, 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(3)),
              // color: Color(0xFFFF1234)
            ),
            child: Stack(
              children: [
                widget.picture,
                Positioned(
                    right: 0,
                    child: GestureDetector(
                      onTap: () {
                        // setState(()
                        // {
                        /// 这里为了实现点击后显示序号，切反选时所有序号都消除，需要以下三步：
                        /// 1、通知view不被选了
                        /// 2、用局部刷新更新界面
                        /// 3、删除保存的key
                        setState(() {
                          widget.selected = FliggyPhotoAlbumManager().selectItemPhoto(
                              context, widget.pic);
                        });


                        // FliggyPhotoAlbumManager().refreshPicView();
                        // FliggyPhotoAlbumManager.deletePicKey();
                        // });
                        widget.clickCallBack();
                      },
                      child: Container(
                        padding: EdgeInsets.fromLTRB(0, 6, 6, 0),
                        color: Color(0x00000000),
                        width: 35,
                        height: 35,
                        // decoration: BoxDecoration(
                        //   borderRadius: BorderRadius.circular(9),
                        // ),
                        child: widget.selected ? Align(
                            alignment: Alignment.topRight,
                            child: Container(
                                width: 17.5,
                                height: 17.5,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Color(0xFFFFDD00),
                                ),

                                child: Align(
                                  alignment: Alignment.topRight,
                                  child: FIcon.Ficon(
                                    // FIcon.ICON_ZHENGQUE,
                                    0xe9b5,
                                    17.5,
                                    Color(0xFF000000),
                                  ),
                                ))) :
                        Align(
                            alignment: Alignment.topRight,
                            child: FIcon.Ficon(
                              // FIcon.ICON_ZHENGQUE,
                              0xea9d,
                              17.5,
                              Colors.white,
                            )),
                      ),
                    )
                ),
              ],))

    );
  }

  void jump2OnePreviewPage(FliggyPictureModel pic) {
    // eventBus.emit(BusEvents.ONE_PICTURE, pic);
    FliggyNavigatorApi.getInstance().push(context, "page://flutter_view/flutter_picture_picker/onePreview", params: {
      "un_flutter": true,
      "flutter_path": AlbumRouter.onePreviewRouter,
      "pic": FliggyPictureModel.Model2Map(pic)
      // "albumName":photoAlbumManager.systemAlbumListMap[albumName]
    });

  }

}