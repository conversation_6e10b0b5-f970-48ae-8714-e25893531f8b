import 'package:ficonfont/ficonfont.dart' as FIcon;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// 用于选中状态局部刷新组件

class FliggyPhotoSelectView extends StatelessWidget {
  bool select;

  FliggyPhotoSelectView({Key? key, required this.select}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 40,
        width: 40,
        child: select
            ? Align(
                child: Container(
                    width: 17.5,
                    height: 17.5,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFFFFDD00),
                    ),
                    child: <PERSON><PERSON>(
                      alignment: Alignment.topRight,
                      child: FIcon.Ficon(
                        // FIcon.ICON_ZHENGQUE,
                        0xe9b5,
                        17,
                        Color(0xFF000000),
                      ),
                    )),
              )
            : <PERSON><PERSON>(
                child: Container(
                    width: 17.5,
                    height: 17.5,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Color(0xFFFFFFFF),
                        border:
                            Border.all(color: Color(0xFF000000), width: 1))),
              ));
  }
}
