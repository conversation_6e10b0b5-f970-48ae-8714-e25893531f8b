import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:fliggy_router/fliggy_router.dart';
import '../../common/album_events.dart';
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';

import 'package:ficonfont/ficonfont.dart' as FIcon;
import 'package:flutter_travel_memories_photo_album/process/upload_pic_process.dart';
import 'package:flutter_travel_memories_photo_album/widgets/album_photo_select.dart';

/// 用于预览照片的页面
/// 入参：List<Picture>
/// 点击预览可以预览效果，点击完成可以上传

class FliggyPhotoPreviewPage extends StatefulWidget {
  FliggyPhotoPreviewPage({Key? key}) : super(key: key);

  @override
  State<FliggyPhotoPreviewPage> createState() {
    return _FliggyPhotoPreviewPageState();
  }
}

class _FliggyPhotoPreviewPageState extends State<FliggyPhotoPreviewPage> {
  PageController _controller = new PageController();

  Map<FliggyPictureModel,Widget> selectPhotoBigWidgets = {};
  List<Widget> selectPhotoSmallWidgets = [];

  // 标题展示文案
  String titleString = "";

  // 偏移量
  var pageOffset = 0.0;

  int titleIndex = 1;

  /// 当前页面展示照片的选中状态
  bool selected = true;

  Map<FliggyPictureModel, bool> selectedPhotosMap = {};
  List<FliggyPictureModel> selectedRealPhotos = [];

  @override
  void initState() {
    super.initState();
    selectedRealPhotos = List.from(FliggyPhotoAlbumManager().selectPhoto);
    for (FliggyPictureModel photo in selectedRealPhotos) {
      selectedPhotosMap[photo] = true;
    }
    _controller.addListener(() {
      print(_controller.page);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Color(0xFFFFFFFF),
        appBar: AppBar(
            elevation: 0.0,
            // 标题居中
            centerTitle: true,
            backgroundColor: Color(0xFFFFFFFF),
            // 左侧返回按钮
            leading: Padding(
              padding: EdgeInsets.only(top: 16, left: 8),
              child: GestureDetector(
                onTap: () {
                  FliggyNavigatorApi.getInstance().pop(context);
                },
                child: FIcon.Ficon(
                    FIcon.ICON_FANHUIJIANTOU, 22, Color(0xFF000000)),
              ),
            ),
            title: Text(
              titleIndex.toString() +
                  '/' +
                  selectedRealPhotos.length.toString(),
              style: TextStyle(fontSize: 18),
            ),

            // 右侧位置展示
            actions: <Widget>[
              // 加了center才能让container脱离appbar的控制，才能控制高度，否则无法控制高度
              Center(
                  child: Padding(
                padding: EdgeInsets.only(right: 9),
                child: GestureDetector(
                    onTap: () {
                      selectedPhotosMap[selectedRealPhotos[titleIndex - 1]] = selectedPhotosMap[
                                  selectedRealPhotos[titleIndex - 1]] ==
                              true
                          ? false
                          : true;
                      setState(() {
                        selected = selectedPhotosMap[selectedRealPhotos[titleIndex - 1]] ==
                                true
                            ? true
                            : false;
                      });
                      //selectedPhotosMap所有选择的图片，而FliggyPhotoAlbumManager().selectPhoto表示实时用于传递，为保证顺序不变每次使用遍历赋值
                      List<FliggyPictureModel> tempList = List.from(selectedRealPhotos);
                      selectedPhotosMap.forEach((key, value) {
                        if (value == false) {
                          tempList.remove(key);
                        }
                      });
                      FliggyPhotoAlbumManager().selectPhoto = tempList;
                      eventBus.emit(BusEvents.EVENT_PREVIEW_SELECT_CHANGE, {});
                    },
                    child: FliggyPhotoSelectView(
                      select: selected,
                    )),
              )),
            ]
            // title: Text('${titleIndex}/${imageUrlList.length}'),
            ),

        // 主体
        body: Container(
          color: Color(0xFFFFFFFF),
          child: Column(
            children: <Widget>[
              // 主图
              Center(
                  child: Container(
                width: 375,
                height: 500,
                color: Color(0xFFD2D4D9),
                child: PageView.builder(
                  onPageChanged: (index) {
                    setState(() {
                      titleIndex = index + 1;
                      selected = selectedPhotosMap[selectedRealPhotos[titleIndex - 1]] ==
                              true
                          ? true
                          : false;
                    });
                  },
                  itemBuilder: (context, index) {
                    if (selectPhotoBigWidgets.length > 0 && selectPhotoBigWidgets.containsKey(selectedRealPhotos[index])) {

                        Widget cachephoto = selectPhotoBigWidgets.containsKey(selectedRealPhotos[index]) ? selectPhotoBigWidgets[selectedRealPhotos[index]]! : Container();
                        return cachephoto;
                    } else {
                      Widget photoWidget = FliggyPhotoAlbumManager().getNativePhoto(
                          context, selectedRealPhotos[index],
                          size: Size(400, 400),
                          fitSys: BoxFit.contain,
                          notneedsize: true);
                      selectPhotoBigWidgets[selectedRealPhotos[index]] = photoWidget;
                      return photoWidget;
                    }
                  },
                  itemCount: selectedRealPhotos.length,
                  controller: _controller,
                ),
              )),

              // 底部
              // Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  // 缩略展示
                  Padding(
                    padding: EdgeInsets.only(top: 19),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Container(
                        height: 82.5,
                        child: Row(
                          children: selectedRealPhotos
                              .map((item) => buildSmallPageItem(
                                  selectedRealPhotos
                                      .indexOf(item),
                                  item))
                              .toList(),
                        ),
                      ),
                    ),
                  ),

                  // 底部按钮
                  Padding(
                      // 位置需要调整
                      padding: EdgeInsets.only(left: 255, top: 25),
                      child:
                      selectTotal()
                          > 0
                          ? MaterialButton(
                              elevation: 0,
                              height: 35.5,
                              minWidth: 81,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(21),
                              ),
                              color: Color(0xFFFFE033),
                              child: Text(
                                "上传  " + selectTotal().toString(),
                                style: TextStyle(),
                              ),
                              disabledColor: Color(0xFF919499),
                              // 按钮的置灰（不可用状态）要靠这里点击事件为null。。。
                              onPressed: () => preUpload())
                          : MaterialButton(
                              elevation: 0,
                              height: 35.5,
                              minWidth: 81,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(21),
                              ),
                              color: Color(0xFFF2F3F5),
                              child: Text(
                                "未选中",
                                style: TextStyle(
                                    color: Color(0xFF919499),
                                    fontSize: 15,
                                    fontWeight: FontWeight.w600),
                              ),
                              onPressed: () => null)
                      )
                ],
              )
            ],
          ),
        ));
  }

  // 生成预览底部小图
  Widget buildSmallPageItem(int index, FliggyPictureModel picture) {

    return Container(
        padding: EdgeInsets.only(left: 9),
        child: Padding(
          padding: EdgeInsets.only(right: 9),
          child: GestureDetector(
              onTap: () => {selectPhoto(index)},
              child: Container(
                  decoration: index == titleIndex - 1
                      ? BoxDecoration(
                          border: new Border.all(
                              color: Color(0xFFFFDD00), width: 6))
                      : null,
                  height: 82.5,
                  width: 82.5,
                  child: selectPhotoSmallWidgets.length > 0
                      ? selectPhotoSmallWidgets[index]
                      : FliggyPhotoAlbumManager()
                          .getPhoto(context, picture, size: Size(82.5, 82.5)))),
        ));
  }
  /// 用于点击缩略图
  selectPhoto(index) {
    print("select index $index");
    _controller.jumpToPage(index);
    setState(() {
      selected =
          selectedPhotosMap[selectedRealPhotos[index]] == true
              ? true
              : false;
    });
  }
  ///改变图片的选择状态
  changePhotoSelect(index, bool selected) {
    selectedPhotosMap[selectedRealPhotos[index]] = selected;
    setState(() {
      selected = selectedPhotosMap[selectedRealPhotos[index]] == true ? true : false;
    });
  }

  int selectTotal() {
    int total = 0;
    selectedPhotosMap.forEach((key, value) {
      if (value == true) {
        total++;
      }
    });
    return total;
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();

    /// 在这个页面离开的时候删除selectedPhotosMap里为false的图片
    selectPhotoBigWidgets = {};
    selectPhotoSmallWidgets = [];
  }

  @override
  void deactivate() {
    super.deactivate();
    // 在页面切换时执行一些清理工作
    selectedPhotosMap.forEach((key, value) {
      if (value == false) {
        selectedRealPhotos.remove(key);
      }
    });
  }
  void preUpload() {
    selectedPhotosMap.forEach((key, value) {
      if (value == false) {
        selectedRealPhotos.remove(key);
      }
    });

    UploadPicProcess().clickFinish(
        context, 2, "one_photo_view_page");

  }
}
