
import 'package:fbridge/fbridge.dart';
import 'package:flutter_travel_memories_photo_album/common/album_events.dart';
import 'package:flutter_travel_memories_photo_album/common/album_native_api.dart';

import '../../util/kv_utils.dart';
import 'album_photo_data_manager.dart';

/// 相册的所有配置
class AlbumGlobalConfig{
  /// 最大可选照片数量，默认9张
  static int MaxSelectedNumber = 9;

  static String backUrl = '';// https://market.wapa.taobao.com/app/trip/rx-travel-footage/pages/detail?source=2&client_type=android&nsTs=1669280082000&nsNs=wifi&disableNav=YES&client_version=**********&dimension=province&titleBarHidden=2&ttid=100000%40travel_android_**********.1830&deviceid=s1ZBdpIcS2xC3m7OpS-CFb_dnmrSq36qTLWltbkJgCzWSUYk8ZgFzswF1rlRyxqa&spm=181.27651411.album.open&dimensionId=340000&_fli_poplayer_count=0&imei=c8e52de3c9968c37&_projVer=1.1.1

  /// 默认不展示城市相册页面
  static bool isNeedCityAlbum = false;
  /// 默认展示全部相册页面
  static bool isNeedPhotoAlbum = true;

  /// 单选模式,默认不开启
  static bool oneSelectType = false;
  /// 使用pictureType这个参数用来控制是否上传oss的，为了和native保持一致才叫这个的，默认是0，不上传oss，不过native上接收这个参数后就给他改名uploadType了
  static int uploadType = 0;
  /// 首页的带城市经纬度信息的接口是否返回
  static bool homeCityDataBack = false;
  /// 用户是否点过开启扫描地点相册永久保存，具体数值从kvcache获取,默认是false，因为此时没有kvcache数据
  static bool openedScanCity = false;
  static List keys = [];

  static List pages = [
    {'key': 'isNeedCityAlbum', 'title': "地点相册"},
    {'key': 'isNeedPhotoAlbum', 'title': "全部相册"},
  ];



  static void setConfigFromParams (Map params) {

    // static可能不会销毁，在这里重置即可
    homeCityDataBack = false;
    // 实际上这个函数并没有传递
    if (params.containsKey('maxSelect')) {
      if (params['maxSelect'] is int) {
        MaxSelectedNumber = params['maxSelect'];
      }
      if (params['maxSelect'] is String) {
        int maxSelect = int.parse(params['maxSelect']);
        MaxSelectedNumber = maxSelect;
      }
    } else {
      MaxSelectedNumber = 9;
    }

    if (params.containsKey('backUrl')) {
      backUrl = params['backUrl'];
    } else if (params.containsKey('referrer')) {
      backUrl = params['referrer'];
    }

    // 根据参数实现页面配置
    /// 城市相册不带就默认没有
    if(params['isNeedCityAlbum'] != null && params['isNeedCityAlbum'] == true || params['isNeedCityAlbum'] == "true" || params['isNeedCityAlbum'] == "1" || params['isNeedCityAlbum'] == 1){
      isNeedCityAlbum = true;
      // 因为城市页面可能不存在，所以在这里接受接口返回的eventbus
      eventBus.on(BusEvents.HOME_EVENT_CITY_UPGRATE_FINISHED, (arg) {
        homeCityDataBack = true;
      });
      bool value = FliggyKVUtil.kv.getBool("openedScanCity");

      if (value) {
        openedScanCity = true;
        FliggyPhotoAlbumManager().mapPhotoPageController =
            MapPhotoPageState.CITY_DATA_LOADING;
        eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
      } else {
        openedScanCity = false;
        FliggyPhotoAlbumManager().mapPhotoPageController =
            MapPhotoPageState.NO_SCAN_ALBUM_PAGE;
        eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
      }


    } else {
      isNeedCityAlbum = false;
    }
    /// 全部相册默认有，不删除就有
    if(params['isNeedPhotoAlbum'] != null && params['isNeedPhotoAlbum'] == false || params['isNeedPhotoAlbum'] == "false"){
      isNeedPhotoAlbum = false;
    } else {
      isNeedPhotoAlbum = true;
    }
    /// 单选模式，这个必须在设置maxSelect之后，单选模式参数优先级高于maxSelect参数
    if(params['selectType'] != null && params['selectType'] == true || params['selectType'] == 1 || params['selectType'] == "1") {
      oneSelectType = true;
      MaxSelectedNumber = 1;
    }
    /// 回传参数类型，默认是0
    /// 0：返回图片的url和上传cdn后的链接
    /// 1：iOS端返回图片的id，安卓端返回图片的path
    if (params['uploadType'] != null && params['uploadType'] == 1 || params['uploadType'] == "1") {
      uploadType = 1;
    } else if (params['uploadType'] != null && params['uploadType'] == 0 || params['uploadType'] == "0"){
      uploadType = 0;
    } else {
      uploadType = 1;
    }
    /// 首页tab的配置，放在这里初始化更合理一点
    getKeys();

  }
  static Map<String,String> toMap() {
    Map<String,String> res = {};
    res["MaxSelectedNumber"] = MaxSelectedNumber.toString();
    res["backUrl"] = backUrl;
    res["isNeedCityAlbum"] = isNeedCityAlbum.toString();
    res["isNeedPhotoAlbum"] = isNeedPhotoAlbum.toString();
    res["selectType"] = oneSelectType.toString();

    return res;
  }

  static void getKeys() {
    keys=[];
    //添加地点相册，且防止重复添加
    if (AlbumGlobalConfig.isNeedCityAlbum && !keys.contains('isNeedCityAlbum')) {
      keys.add('isNeedCityAlbum');
    }
    //添加全部相册，且防止重复添加
    if (AlbumGlobalConfig.isNeedPhotoAlbum && !keys.contains('isNeedPhotoAlbum')) {
      keys.add('isNeedPhotoAlbum');
    }
  }

}