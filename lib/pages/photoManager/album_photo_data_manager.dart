import 'dart:convert' as convert;
import 'dart:convert';
import 'dart:core';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter_travel_memories_photo_album/common/album_events.dart';
import 'package:flutter_travel_memories_photo_album/common/album_native_api.dart';
import 'package:flutter_travel_memories_photo_album/models/bridge_callback_data_model.dart';
import 'package:flutter_travel_memories_photo_album/models/city_album_model.dart';
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:flutter_travel_memories_photo_album/models/system_album_model.dart';
import 'package:flutter_travel_memories_photo_album/net/album_request_city_data.dart';
import 'package:flutter_travel_memories_photo_album/widgets/album_photo_view.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_global_config.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:fbridge/fbridge.dart';
import 'package:ftoast/ftoast.dart';
import 'package:fsuper/fsuper.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:fliggy_mtop/fliggy_mtop.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';

class FliggyPhotoAlbumManager {
  static final FliggyPhotoAlbumManager _instance =
      FliggyPhotoAlbumManager._internal();

  //   const MethodChannel _channel = const MethodChannel('flutter_travel_share');

  ///工厂构造函数
  factory FliggyPhotoAlbumManager() {
    return _instance;
  }

  ///构造函数私有化，防止被误创建
  FliggyPhotoAlbumManager._internal();

  // 缩略图列表
  //   List<Uint8List?> thumbnailDatas = [];
  // 全部图片信息列表，在iOS中取的是照片数量最多的相册，安卓中是收集所有相册中的照片
  // List<FliggyPictureModel> allPicturesList = [];

  // 带城市信息的全部照片，相当于一层缓冲，防止在首页二次刷新
  List<FliggyPictureModel> allPicturesListWithCity = [];

  // 经纬度信息
  List<Map> latlongData = [];

  // 相册授权,这里默认兜底是false，必须手动更新状态
  // 23.11.23,这块如果是默认 false,会导致 iOS 进没有授权页面,在那个页面监听了生命周期,但是 iOS 端监听的是容器层生命周期,会导致多次请求接口
  bool photoPermission = true;

  // 列表页首次进入
  bool albumPageIsFirst = true;
  // 按系统信息分类后的相册列表
  Map<String, FliggySystemAlbumModel> systemAlbumListMap = new Map();

  // 按城市信息分类后的相册列表
  Map<String, FliggyCityAlbumModel> cityAlbumListMap = new Map();

  // 是否有缓存数据
  bool cacheHasData = false;

  // 照片中是否有经纬度
  bool photoHasData = false;

  // 选中的照片组，实际上用于页面间传递
  List<FliggyPictureModel> selectPhoto = [];

  // 给H5的回调数据
  late BridgeCallbackDataModel callbackData = new BridgeCallbackDataModel();
  String lagLngData = '';
  
  // 是否获取了只有首个相册的桥
  bool hasGetAlbumFirst = false;

  // 是否获取了全部相册的桥
  bool hasGetAllAlbum = false;
  // 地点相册的总控制器，放在controller里经常刷新不到位，只能抽出来控制
  // 为了这个，将整个控制器改成单例了
  MapPhotoPageState mapPhotoPageController =
      MapPhotoPageState.NO_PHOTOS_PERMISSION;

  // 全部相册当前展示的相册
  String currentLocalAlbum = "";

  void init() {
    print("初始化啦");
    latlongData = [];

    photoPermission = true;

    systemAlbumListMap = new Map();

    cityAlbumListMap = new Map();

    cacheHasData = false;

    photoHasData = false;

    selectPhoto = [];

    callbackData = new BridgeCallbackDataModel();

    hasGetAlbumFirst = false;

    hasGetAllAlbum = false;

    albumPageIsFirst = true;
  }

  // 通过桥获取所有照片
  // 由于需要上下文，只能在这里初始化
  Future getPhotoWithFBridge(
      BuildContext context, VoidCallback onSuccess) async {
    if (hasGetAllAlbum) {
      return true;
    }
    // 异步获取不带经纬度的照片（接口返回较慢）
    var startTime = DateTime.now();
    print("<fphoto>耗时分析：获取全部图片数据处理开始:${DateTime.now()}");
    await getPhotoList(context, false);
    print("<fphoto>耗时分析：获取全部图片数据处理结束:${DateTime.now()}");
    onSuccess();
    if (AlbumGlobalConfig.isNeedCityAlbum) {
      // 如果需要城市相册，则异步获取带经纬度的照片（接口返回较慢）
      await getCityPhotoList(true,context: context);
    }
    hasGetAllAlbum = true;
    return true;
  }
  // 进入相册首页,先获取指定相册
  Future getAsignPhotoWithFBridge(
      BuildContext context, VoidCallback onSuccess) async {
    await FBridgeApi.newInstance(context).call("open_loading_view");
    await getAsignPhotoList(context, false,0);
    await FBridgeApi.newInstance(context).call("close_loading_view");
    onSuccess();
    hasGetAlbumFirst = true;
  }

  // 获取剩余全部照片
  Future<void> getPhotoList(BuildContext context, bool needLocation) async {
    var startTime = DateTime.now();
    print("耗时分析：数据处理开始:${DateTime.now()},定位：${needLocation.toString()}");
    Map res = await FBridgeApi.newInstance(context).call(
        AlbumNativeApi.getAsignPhotosForFliggyAlbum, {"needLatlan": needLocation,"bridgeType":1});
    // 如果全部相册为空，则整合处理全部照片[{name:'',photo:[]},]
    print("耗时分析：native相册数据接口请求返回:${DateTime.now().difference(startTime)}");
    List albumList = res["albumList"];
    // 先按系统相册分类
    sortPhotoAction(albumList);
    print("耗时分析：按系统相册分类:${DateTime.now().difference(startTime)}");
    // cityAlbumListMap = {};
    // sortPhotoByCity();
    // selectPhoto = [];
    // print("耗时分析：按城市分类:${DateTime.now().difference(startTime)}");
  }

  Future<void> getAsignPhotoList(BuildContext context, bool needLocation,int index) async {
    var startTime = DateTime.now();
    print("<fphoto>耗时分析：数据处理开始:${DateTime.now()},定位：${needLocation.toString()}");
    Map res = await FBridgeApi.newInstance(context).call(
        AlbumNativeApi.getAsignPhotosForFliggyAlbum, {"needLatlan": needLocation,"index":index,"bridgeType":0});
    print("<fphoto>耗时分析：native相册数据接口请求返回:${DateTime.now().difference(startTime)}");
    List albumList = res["albumList"];
    systemAlbumListMap = {};
    // 先按系统相册分类
    sortPhotoAction(albumList);
    print("<fphoto>耗时分析：按系统相册分类:${DateTime.now().difference(startTime)}");
    // cityAlbumListMap = {};
    // sortPhotoByCity();
    selectPhoto = [];
    // print("耗时分析：按城市分类:${DateTime.now().difference(startTime)}");
  }

  /// 获取城市相册列表，目的是只获取城市信息照片，同时不刷新照片
  getCityPhotoList(bool needLocation,{required BuildContext context}) async {
    var startTime = DateTime.now();
    print("<fphoto>耗时分析：数据处理开始:${DateTime.now()},定位：${needLocation.toString()}");
    Map res = await FBridgeApi.newInstance(null).call(
        AlbumNativeApi.getPhotosForFliggyAlbum, {"needLatlan": needLocation});
    // 如果全部相册为空，则整合处理全部照片[{name:'',photo:[]},]
    print("<fphoto>耗时分析：native相册数据接口请求返回:${DateTime.now().difference(startTime)}");
    List albumList = res["albumList"];

    // 先按系统相册分类
    // sortPhotoAction(albumList);
    allPicturesListWithCity = [];
    for (int i = 0; i < albumList.length; i++) {
      // 只是一个临时变量
      List cacheMap = albumList[i]['photos'];
      if (cacheMap.length == 0) {
        continue;
      }

      // systemAlbumListMap[albumList[i]['name']] = [];
      cacheMap.forEach((element) {
        allPicturesListWithCity.add(FliggyPictureModel().Map2Model(element));
      });
    }
    // 按时间排序
    // allPicturesListWithCity
    //     .sort((a, b) => b.createTime.compareTo(a.createTime));

    cityAlbumListMap = {};
    sortPhotoByCity(context: context);

    // selectPhoto = [];
    print("耗时分析：按城市分类:${DateTime.now().difference(startTime)}");
    // mapPhotoPageController = MapPhotoPageState.NORMAL_ALBUM_PAGE;
    // 如果是城市信息接口返回数据，需要通知刷新页面
    // if (needLocation) {
    //   eventBus.emit(BusEvents.HOME_EVENT_CITY_UPGRATE_FINISHED);
    // }
  }
  // 判断是否是纯数字-是否是时间戳
  bool isAllDigits(String str) {
    final pattern = r'^\d+$';
    final regExp = RegExp(pattern);
    return regExp.hasMatch(str);
  }
  // 时间转化
  String dateConvert(String dateString) {
    final format = DateFormat("yyyy:MM:dd HH:mm:ss");
    final date = format.parse(dateString);
    final newFormat = DateFormat("yyyy年MM月dd日");
    return newFormat.format(date);
  }
  void removeDuplicates() {
    cityAlbumListMap.forEach((key, value ) {
      Set<FliggyPictureModel> modelSet = value.allPicturesList.toSet();
      value.allPicturesList = modelSet.toList()..sort((a, b) => b.createTime.compareTo(a.createTime));
      value.photonumber = value.allPicturesList.length;
      // 如果是时间戳
      String startData = "";
      String endData = "";
      if (isAllDigits(value.allPicturesList[value.allPicturesList.length - 1].createTime)) {
        startData = value.allPicturesList[value.allPicturesList.length - 1].createTime.toString().length > 13
            ? value.allPicturesList[value.allPicturesList.length - 1].createTime.toString()
            : DateFormat('yyyy年MM月dd日').format(
            DateTime.fromMillisecondsSinceEpoch(int.parse(
                value.allPicturesList[value.allPicturesList.length - 1].createTime.toString())));

        endData = value.allPicturesList[0].createTime.toString().length > 13
            ? value.allPicturesList[0].createTime.toString()
            : DateFormat('yyyy年MM月dd日').format(
            DateTime.fromMillisecondsSinceEpoch(int.parse(
                value.allPicturesList[0].createTime.toString())));
      } else {
        // 如果不是时间戳,是 yyyy:MM:nn hh:mm:ss
        startData = dateConvert(value.allPicturesList[value.allPicturesList.length - 1].createTime.toString());
        endData = dateConvert(value.allPicturesList[0].createTime.toString());
      }

      value.time = startData + "-" + endData;
    });
  }
  // 对照片按系统相册分类
  void sortPhotoAction(List albumList) {
    // print(allPicturesList);
    for (int i = 0; i < albumList.length; i++) {
      // 只是一个临时变量
      List cacheMap = albumList[i]['photos'];
      if (cacheMap.length == 0) {
        continue;
      }
      List<FliggyPictureModel> specAlbumList = [];
      // systemAlbumListMap[albumList[i]['name']]!.albumList = [];
      cacheMap.forEach((element) {
        specAlbumList.add(FliggyPictureModel().Map2Model(element));
        // systemAlbumListMap[albumList[i]['name']]!.albumList
        //     .add(FliggyPictureModel().Map2Model(element));
      });
      // 每个相册中按时间排序
      specAlbumList.sort((a, b) => (b.createTime).compareTo(a.createTime));
      String name = albumList[i]['name'];
      String photosNum = albumList[i]['photosNum'].toString();
      if (photosNum == "null") {
        photosNum = specAlbumList.length.toString();
      }
      FliggySystemAlbumModel album = new FliggySystemAlbumModel(albumList: specAlbumList, albumNum: photosNum, albumName: name);
      systemAlbumListMap[albumList[i]['name']] = album;
    }
  }
  // 解析缓存中首个相册和全部相册列表数据
  void getFirstAlbumListMapFromJson (String jsonString) {
    // 缓存中是相册列表
    List albumList = jsonDecode(jsonString);
    for (int i = 0;i < albumList.length; i++) {
      // 每一个相册
      Map albumMap = albumList[i];
      List photosList = albumMap['photos'];
      if (photosList.length == 0) {
        return;
      }
      List<FliggyPictureModel> specAlbumList = [];
      photosList.forEach((element) {
        specAlbumList.add(FliggyPictureModel().Map2Model(element));
        // systemAlbumListMap[albumList[i]['name']]!.albumList
        //     .add(FliggyPictureModel().Map2Model(element));
      });
      // 每个相册中按时间排序
      specAlbumList.sort((a, b) => (b.createTime).compareTo(a.createTime));
      String name = albumMap['name'];
      String photosNum = albumMap['photosNum'].toString();
      if (photosNum == "null") {
        photosNum = specAlbumList.length.toString();
      }
      FliggySystemAlbumModel album = new FliggySystemAlbumModel(albumList: specAlbumList, albumNum: photosNum, albumName: name);
      systemAlbumListMap[albumMap['name']] = album;
    }
  }
  // 对照片按城市分类
  void sortPhotoByCity(
      {required BuildContext context ,String? mapDataJsonStr, VoidCallback? onFinish}) async {
    photoHasData = false;

    /// 1、扫描相册
    /// 2、用mtop接口获取城市信息
    /// 3、判断城市信息是否全，并存kvcache
    /// 4、根据返回的数据将照片分类，这块调用照片管理类的接口就行
    // 1、扫描相册
    // [{\"lat\":\"30.281\",\"lng\":\"119.998\"},{\"lat\":\"30.280\",\"lng\":\"119.998\"}]
    List<Map> latlongData = FliggyPhotoAlbumManager().getlatlongData();
    // 将数据以20分片，用于后期请求
    List<List<Map>> res = [];
    if (latlongData.length > 20) {
      for (int i = 0; i < latlongData.length; i += 20) {
        res.add(latlongData.sublist(
            i, i + 20 > latlongData.length ? latlongData.length : i + 20));
      }
    } else {
      res = [latlongData];
    }

    List<String> resJsonStringList = [];
    res.forEach((element) {
      String jsonString = json.encode(element);
      resJsonStringList.add(jsonString);
    });

    // 如果扫描后的相册中有经纬度数据，则跳列表页，否则跳无数据页
    if (latlongData.length < 1) {
      eventBus.emit(BusEvents.EVENT_CITY_UPGRATE_FINISHED, 0);
      // 如果没有经纬度信息，则提醒没有从相册获取到经纬度
        FliggyPhotoAlbumManager().mapPhotoPageController =
            MapPhotoPageState.NO_CITY_DATA_PAGE;
        eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
      return;
    } else {
      eventBus.emit(BusEvents.HAVE_CITY_DATA);
    }

    // 2、获取城市信息
    // 这里如果用开启地点相册的context访问mtop，是会报错的，
    // 这里不能一次性将所有照片进行请求，会挤爆mtop，需要进行分段请求
    FliggyRequestCityData.requestCityData(
        context:context,
        needSave: false,
        geoLocationJson: resJsonStringList,
        onSuccess: (List allData) async {
          if (allData != null && allData.isNotEmpty) {
            /// 这里获取到的是去重后的latlongData对应的经纬度，不是照片对应的经纬度，不能直接使用。。。
            // 有数据
            // 将经纬度-城市信息拼成map，转成json，再保存给kvacahe
            Map latlong_cityMap = {};
            for (int i = 0; i < allData.length; i++) {
              String latlongString = FliggyPhotoAlbumManager().latlongData[i]["lat"] + "," + FliggyPhotoAlbumManager().latlongData[i]["lng"];
              latlong_cityMap[latlongString] = allData[i]["cityName"];
            }
            // latlong_cityMap不为空，存kvcache
            print(latlong_cityMap);
            if (latlong_cityMap.isNotEmpty) {
              String jsonStr = json.encode(latlong_cityMap);
              lagLngData = jsonStr;
              sortCityData(jsonStr);
              mapPhotoPageController = MapPhotoPageState.NORMAL_ALBUM_PAGE;
              eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
            } else {
              eventBus.emit(BusEvents.EVENT_CITY_UPGRATE_FINISHED, 0);
              mapPhotoPageController = MapPhotoPageState.NO_CITY_DATA_PAGE;
              eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
            }
          } else {
            // 没有数据
            eventBus.emit(BusEvents.EVENT_CITY_UPGRATE_FINISHED, 0);
            mapPhotoPageController = MapPhotoPageState.NET_ERROR_PAGE;
            eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
          }
        },
        onFailed: (MtopResponseModel failedModel) {
          // 经纬度信息不在cache缓存了，但是为了临时解决开启地点相册页面的context无法访问服务端接口的问题，先将该变量保存在
          // 静态变量中，也算在访问服务端接口失败后有一个兜底
          if (lagLngData.length == 0) {
            mapPhotoPageController = MapPhotoPageState.NET_ERROR_PAGE;
            eventBus.emit(BusEvents.NET_ERROR, 0);
            eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
            eventBus.emit(BusEvents.EVENT_CITY_UPGRATE_FINISHED, 0);
          } else {
            sortCityData(lagLngData);
          }
          // eventBus.emit(BusEvents.EVENT_CITY_UPGRATE_FINISHED, 0);
        }).catchError((_) {
        // 请求失败
        eventBus.emit(BusEvents.NET_ERROR, 0);
        mapPhotoPageController = MapPhotoPageState.NET_ERROR_PAGE;
        eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
        eventBus.emit(BusEvents.EVENT_CITY_UPGRATE_FINISHED, 0);
    });
  }

  ///按照城市分类相册数据
  sortCityData(mapDataJsonStr) {
    // 安卓和iOS通过methodchannel传过来的都是string类型字符串，用这个方法转化成map
    Map keyMap = convert.jsonDecode(mapDataJsonStr);
    // 获取精度
    int percisionNullable = keyMap!['percision'] ?? 3;
    int percision = percisionNullable;
    // 遍历图片
    for (int i = 0; i < allPicturesListWithCity.length; i++) {
      // 根据精度获取经纬度
      double latitude = allPicturesListWithCity[i].latitude;
      double longitude = allPicturesListWithCity[i].longitude;
      String latitudeString = formatNum(latitude, percision);
      String longitudeString = formatNum(longitude, percision);

      if (latitude == 0 && longitude == 0) {
        // 对照片来说，如果没有经纬度则跳过该照片
        continue;
      } else {
        photoHasData = true;
        // 照片经纬度不为0
        // 拼接经纬度字符串，无需拆字典了
        String key = latitudeString + ',' + longitudeString;
        String city = keyMap![key] ?? '';
        // 如果经纬度-城市缓存 字典中有这个经纬度key，加到城市map中
        if (keyMap.containsKey(key)) {
          // 如果城市相册里有这个城市，将该照片加入到该城市
          if (cityAlbumListMap.containsKey(city)) {
            cityAlbumListMap[city]!
                .allPicturesList
                .add(allPicturesListWithCity![i]);
            cityAlbumListMap[city]!.photonumber += 1;
          } else {
            cityAlbumListMap[city] = FliggyCityAlbumModel(
                location: city,
                allPicturesList: [allPicturesListWithCity![i]],
                photonumber: 1,
                time: allPicturesListWithCity![i].createTime.toString().length > 13
                    ? allPicturesListWithCity![i].createTime.toString()
                    : DateFormat('yyyy年MM月dd日').format(
                        DateTime.fromMillisecondsSinceEpoch(int.parse(
                            allPicturesListWithCity![i].createTime.toString()))));
          }
        } else {
          continue;
        }
      }
    }
    eventBus.emit(
        BusEvents.EVENT_CITY_UPGRATE_FINISHED, cityAlbumListMap.length);
    // 对每个相册去重,防止重复
    removeDuplicates();
  }

  /// 获取小数点后num位的数据
  String formatNum(double num, int percision) {
    if ((num.toString().length - num.toString().lastIndexOf(".") - 1) <
        percision) {
      //小数点后有几位小数
      return num.toStringAsFixed(percision)
          .substring(0, num.toString().lastIndexOf(".") + percision + 1)
          .toString();
    } else {
      return num.toString()
          .substring(0, num.toString().lastIndexOf(".") + percision + 1)
          .toString();
    }
  }

  double getRandomDouble(double min, double max) {
    Random random = new Random();
    return double.parse(
        (min + random.nextDouble() * (max - min)).toStringAsFixed(3));
  }

  /// 扫描经纬度信息
  List<Map> getlatlongData() {
    // 就是一个用来查重的列表，没有实际意义
    List<String> keyList = [];
    // 这里先清一下，要不然一直点会一直增加
    latlongData = [];
    // 遍历照片
    for (int i = 0; i < allPicturesListWithCity!.length; i++) {
      // 根据精度获取经纬度
      double latitude = allPicturesListWithCity![i].latitude;
      double longitude = allPicturesListWithCity![i].longitude;
      // 这里精度遵循原有逻辑保留三位小数
      String latitudeString = formatNum(latitude, 3);
      String longitudeString = formatNum(longitude, 3);

      if (latitude == 0 && longitude == 0) {
        // 对照片来说，如果没有经纬度则跳过该照片
        // 这个地方除了水和一个浮标啥都没有，这里假设所有飞猪用户不会到这里。这样做可以减少后续操作时间
        continue;
      } else {
        // 照片经纬度不为0
        // 这里由于是获取城市信息，不需要像查询时将经纬度信息拼字符串，而是将经纬度信息和时间保存为map，再存在list里就行了
        // 最终数据格式：[{"lat":"30.278","lng":"120.003","checkTime":"1111111111"},{"lat":"52.463","lng":"-1.496","checkTime":"1111111111"},{"lat":"0","lng":"0"}]
        // 实际上这里不需要这个checktime，先不拼了
        // 为了查重
        String key = latitudeString + ',' + longitudeString;
        if (keyList.contains(key)) {
          // 如果重复，不做
          continue;
        } else {
          // 否则，增加这一项
          keyList.add(key);
          // 单个经纬度信息
          Map keyMap = {"lat": latitudeString, "lng": longitudeString};
          latlongData.add(keyMap);
        }
        // 如果经纬度-城市缓存 字典中有这个经纬度key，加到城市map中
      }
    }
    return latlongData;
  }

  /// 网格布局缩略图片获取
  Widget getPhotoItems(BuildContext context, int index,
      FliggyPictureModel picture, VoidCallback clickCallBack) {
    return FliggyPhotoView(
      picture: getPhoto(context, picture),
      clickCallBack: () => clickCallBack(),
      index: index,
      pic: picture,
      selected: FliggyPhotoAlbumManager().searchPicInList(picture),
    );
  }

  bool selectItemPhoto(
      BuildContext context, FliggyPictureModel picture) {
    // 如果选中的列表中存在：
    if (selectPhoto.any((element) => element.id == picture.id)) {
      selectPhoto.removeWhere((photo) => photo.id == picture.id);
      return false;
    } else {
      if (selectPhoto.length >= AlbumGlobalConfig.MaxSelectedNumber) {
        // 弹窗
        FToast.toast(
          context,
          toast: FSuper(
            text: "最多只能选${AlbumGlobalConfig.MaxSelectedNumber}张",
            style: TextStyle(color: Colors.black),
            padding: EdgeInsets.all(12),
            backgroundColor: Colors.white,
          ),
          duration: 3000,
        );
        return false;
      } else {
        // 如果列表中不存在，则添加
        selectPhoto.add(picture);
        return true;
      }
    }
  }

  /// 用于仅查询图片是否被选中
  bool searchPicInList(FliggyPictureModel picture) {
    if (selectPhoto.any((element) => element.id == picture.id)) {
      return true;
    }
    return false;
  }

  /// 获取图片在列表中排序
  int getPicNum(FliggyPictureModel picture) {
    for (int i = 0; i < selectPhoto.length; i++) {
      if (selectPhoto[i] == picture) {
        return i + 1;
      }
    }
    return 9;
  }

  /// iOS图片构建方法，验证可用，性能优秀
  Future<Uint8List?> getThumbnail(
      BuildContext context, FliggyPictureModel picture,
      {Size? size}) async {
    Map res = await FBridgeApi.newInstance(context).call(
        AlbumNativeApi.getImageDataForFliggyAlbum, {
      "photoId": picture.id,
      if (size != null)
      "photoWidth": size.width,
      if (size != null)
      "photoHeight": size.height,
    });
    String u8str = res["data"];
    Uint8List bytes = base64.decode(u8str);
    return bytes;
  }

  Map<String, Uint8List> thumbCache = {};

  /// 获取图片，列表页cell可用
  Widget getPhoto(BuildContext context, FliggyPictureModel picture,
      {Size? size, BoxFit? fitSys, bool? notneedsize}) {
    if (Platform.isIOS) {
      // 如果缓存中有该照片二进制数据，则直接取
      if (thumbCache.containsKey(picture.id)) {
        return Image.memory(
          thumbCache![picture.id]!,
          width: 240,
          height: 240,
          // fit: BoxFit.cover,
          fit: fitSys != null ? fitSys : BoxFit.cover,
        );
      } else {
        if (thumbCache.length > 500) {
          thumbCache = {};
        }
        return FutureBuilder<Uint8List?>(
          future: getThumbnail(context, picture,
              size: notneedsize != null ? null : Size(480, 480)),
          initialData: null,
          builder: (BuildContext context, AsyncSnapshot<Uint8List?> snapshot) {
            if (snapshot.data == null) {
              return Container(
                  width: 240,
                  height: 240,
                  color: Colors.grey[200]!.withAlpha(50));
            } else {
              thumbCache[picture.id] = snapshot.data!;
              return Image.memory(
                snapshot.data!,
                width: 240,
                height: 240,
                fit: fitSys != null ? fitSys : BoxFit.cover,
              );
            }
          },
        );
      }
    } else {
      return Image(
          // color: Colors.blue,
          width: 100,
          height: 100,
          image: ResizeImage(
            FileImage(
              File(picture.path),
            ),
            width: size != null ? size.width.toInt() : 220,
            allowUpscaling: true,
          ),
          // fit: BoxFit.cover
          fit: fitSys != null ? fitSys : BoxFit.cover);
    }
  }

  /// 获取图片，预览页使用
  Widget getNativePhoto(BuildContext context, FliggyPictureModel picture,
      {Size? size, BoxFit? fitSys, bool? notneedsize}) {
    if (Platform.isIOS) {
      if (picture.path != null && picture.path.length > 0) {
        return Image.file(
          File(picture.path),
          fit: fitSys != null ? fitSys : BoxFit.cover,
        );
      } else {
        return FutureBuilder<Uint8List?>(
          future: getThumbnail(context, picture,size: Size(750, 1200)),
          initialData: null,
          builder: (BuildContext context, AsyncSnapshot<Uint8List?> snapshot) {
            return snapshot.data == null
                ? Container(width: 375, height: 600, color: Color(0xFFD2D4D9))
                :
            Container(
              decoration: BoxDecoration(color: Color(0xFFD2D4D9)),
                child: Image.memory(
              snapshot.data!,
              fit: fitSys != null ? fitSys : BoxFit.cover,
            ))
           ;
          },
        );
      }
    } else {
      return Image(
          image:
              // ResizeImage(
              FileImage(
            File(picture.path),
          ),
          //   width: 375,
          // ),

          // fit: BoxFit.cover
          fit: fitSys != null ? fitSys : BoxFit.cover);
    }
  }

  /// uploadtype为0时点完成的回调函数
  void callBackPics(BuildContext context, {int? popnum}) {
    FliggyUserTrackApi.getInstance().custom(context, "uploadPicWithPath", "", {});
    if (Platform.isIOS) {
      /// 告诉native是从flutter相册来的
      Map res = {"isFromFlutterPhotoPicker": "true"};
      List picIdList = [];
      for (int i = 0; i < selectPhoto.length; i++) {
        picIdList.add(selectPhoto[i].id);
      }
      res["selected_assets_urls"] = picIdList;

      if (popnum == 1) {
        FliggyNavigatorApi.getInstance().pop(context, result: res);
      } else if (popnum == 2) {
        FliggyNavigatorApi.getInstance().pop(context);
        FliggyNavigatorApi.getInstance().pop(context, result: res);
      }
    } else if (Platform.isAndroid) {
      Map res = {};
      List picIdList = [];
      // 咱也不知道为啥安卓要两套返回逻辑，只能照做
      List photoPathList = [];
      for (int i = 0; i < selectPhoto.length; i++) {
        Map temp = {};
        temp["url"] = selectPhoto[i].path;
        temp["mediaType"] = "media_photo";
        picIdList.add(temp);
        photoPathList.add(selectPhoto[i].path);
      }
      res["mediaInfos"] = picIdList;
      res["photos"] = photoPathList;
      FliggyNavigatorApi.getInstance()
          .popTo(context, AlbumGlobalConfig.backUrl, result: res);
    }
  }
}

enum MapPhotoPageState {
  // 未扫描相册：缓存数据为空，且开启了权限，且没有扫描过
  NO_SCAN_ALBUM_PAGE,
  // 正常展示地图相册:缓存数据不为空且开启了权限
  NORMAL_ALBUM_PAGE,
  // 扫描了相册，依然没有信息：缓存数据为空，开启了权限，且扫描过至少一次
  SCAN_NO_DATA_PAGE,
  // 网络问题导致接口失败，显示网络失败的提示页面
  NET_ERROR_PAGE,
  // 没有相册权限
  NO_PHOTOS_PERMISSION,
  // 地点相册的LOAADING页面
  CITY_DATA_LOADING,
  // 没有地点信息的提示页面
  NO_CITY_DATA_PAGE,
  // 相当于兜底逻辑，忘了是啥原因了，只文案和第一个不同，实际上还是刷新逻辑
  EXPOSE_PAGE,
}
