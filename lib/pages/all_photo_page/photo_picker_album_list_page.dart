
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/spm/picture_picker_spm.dart';

import 'photo_picker_album_list_view_item.dart';
class FliggyLocalPhotoAlbumListPage extends StatefulWidget {
  // final FliggyPhotoAlbumManager() photoAlbumManager;

  String currentAlbum;
  Function(String album) clickCallBack;
  FliggyLocalPhotoAlbumListPage({Key? key,required this.currentAlbum,required this.clickCallBack}) : super(key: key);

  @override
  State<FliggyLocalPhotoAlbumListPage> createState() => _FliggyLocalPhotoAlbumListPage();
}

class _FliggyLocalPhotoAlbumListPage extends State<FliggyLocalPhotoAlbumListPage> {

  // 页面入参
  // final FliggyPhotoAlbumManager() photoAlbumManager;

  // _FliggyLocalPhotoAlbumListPage({required this.photoAlbumManager});

  @override
  void initStage() {
    super.initState();
    // 以后初始化数据用
    // 页面埋点
    FliggyUserTrackApi.getInstance().pageEnter(
        context, PicturePickerSpm.allPicListPage("0"), AlbumRouter.localPhotoListRouter,
        args: {});
  }

  @override
  Widget build(BuildContext context) {
    return
    GestureDetector(
      onTap: () {
        // 点空白返回的本质是选择了当前相册,不能返回空
        widget.clickCallBack(widget.currentAlbum);
      },
      child: Container(
        color: Color(0x99000000),
        child:
        Align(
          alignment: Alignment.topCenter,
          child: Container(
            height: 512,
            color: Color(0xFFFFFFFF),
            child: ListView(
              // 确定元素滚动方向属性，节约性能，不加这个会导致快速滚动时页面闪白
              itemExtent:128,
              children: _listView(),
            ),
          ),),
      )
    );

  }

  List<Widget> _listView() {
    List<Widget>listWidget = [];
    FliggyPhotoAlbumManager().systemAlbumListMap.forEach((albumName, value) {
      // 每个数组元素
      if (albumName == widget.currentAlbum) {
        listWidget.add(localPhotoAlbumCell(albumName: albumName,clickCallBack: widget.clickCallBack, select: true,));
      } else {
        listWidget.add(localPhotoAlbumCell(albumName: albumName,clickCallBack: widget.clickCallBack,select: false,));
      }
    });
    return listWidget;
  }



}
