import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:ficonfont/ficonfont.dart' as FIcon;
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';

class localPhotoAlbumCell extends StatelessWidget {
  // final FliggyPhotoAlbumManager() photoAlbumManager;

  String albumName;
  Function(String album) clickCallBack;
  bool select;

  localPhotoAlbumCell(
      {Key? key,
      required this.albumName,
      required this.clickCallBack,
      required this.select})
      : super(key: key);

  @override
  Widget cellContentView(BuildContext context) {
    // Uint8List a = Uint8List.fromList([]);
    // Uint8List thumbnailData = await photoAlbumManager.systemAlbumListMap[albumName]![0].thumbnail().then((value) {
    // });
    List<FliggyPictureModel>? PhotoList =
        FliggyPhotoAlbumManager().systemAlbumListMap[albumName]!.albumList;
    if (PhotoList!.length == 0) {
      return Container();
    }
    return Container(
      height: 111,
      width: 375,
      child: Row(
        children: <Widget>[
          Container(
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(3)),
              // color: Color(0xFFFF1234)
            ),
            width: 75,
            height: 75,
            child: FliggyPhotoAlbumManager().getPhoto(context,
                FliggyPhotoAlbumManager().systemAlbumListMap[albumName]!.albumList[0]),
          ),
          Padding(
              padding: EdgeInsets.only(left: 20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  //相册信息
                  // 名称超长...,这里因为是 column,所以用 expand 不方便,220 是给右边的对钩也留了位置
                  ConstrainedBox(constraints: BoxConstraints(maxWidth: 220),
                      child: Text(
                        albumName,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xff000000),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )),
                  Padding(
                      padding: EdgeInsets.only(top: 9),
                      child: Text(
                        FliggyPhotoAlbumManager()
                            .systemAlbumListMap[albumName]!.albumNum
                            +
                            "图",
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                          color: Color(0xff8E8E8E),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )),
                ],
              )),
          // 用来扩展挤压占位
          Expanded(child: Container()),
          select ?
          FIcon.Ficon(
            // FIcon.ICON_ZHENGQUE,
            0xe91b,
            20,
            Color(0xFF333333),
          ) : Container(),
        ],
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {
        clickCallBack(albumName);
      },
      // height: 130.0,
      title: Column(
        children: <Widget>[
          // 内容视图
          Container(
            padding: EdgeInsets.symmetric(horizontal: 1.0, vertical: 6.0),
            child: cellContentView(context),
          ),
          // 分割线
          Container(
            margin: EdgeInsets.only(top: 4.0),
            color: Color(0xffD8D8D8),
            constraints: BoxConstraints.expand(height: 0.5),
          )
        ],
      ),
    );
  }


}
