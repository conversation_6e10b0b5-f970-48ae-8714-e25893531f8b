import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:fbridge/fbridge.dart';
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:flutter_travel_memories_photo_album/spm/picture_picker_spm.dart';
import 'package:flutter_travel_memories_photo_album/widgets/album_photo_tool_view.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';

import '../../common/album_events.dart';
/// 全部相册页面

class FliggyAllPhotoPage extends StatefulWidget {
  // final FliggyPhotoAlbumManager() photoAlbumManager;
  String albumName;
  FliggyAllPhotoPage({Key? key,required this.albumName}) : super(key: key);

  @override
  State<FliggyAllPhotoPage> createState() => FliggyAllPhotoPageState();
}
class FliggyAllPhotoPageState extends State<FliggyAllPhotoPage> with FliggyPageMixin {
  // 页面入参
  // final FliggyPhotoAlbumManager() photoAlbumManager;

  List<FliggyPictureModel> selectPhoto = [];
  var contentViewKey;
  //用于处理预览页面的回调
  late void Function(dynamic) eventCallback;
  @override
  void initState() {
    contentViewKey = new GlobalKey<FliggyPhotoToolViewState>();
    super.initState();
    FliggyUserTrackApi.getInstance().pageEnter(
        context, PicturePickerSpm.allPicPage("0"), AlbumRouter.homeRouter,
        args: {});
    eventCallback = (datas) {
      if (mounted) {
        setState(() {});
      }
    };
    eventBus.on(BusEvents.EVENT_PREVIEW_SELECT_CHANGE,eventCallback);
  }
  @override
  Widget build(BuildContext context) {
    // 如果数据已经返回
    // 如果新数据里没有数据，就返回空
    if (FliggyPhotoAlbumManager().systemAlbumListMap[widget.albumName] == null) {
      return SizedBox.shrink();
    }
    if (FliggyPhotoAlbumManager().systemAlbumListMap[widget.albumName]!.albumNum == FliggyPhotoAlbumManager().systemAlbumListMap[widget.albumName]!.albumList.length.toString()) {
      FliggyPhotoAlbumManager().albumPageIsFirst = false;
      FBridgeApi.newInstance(context).call("close_loading_view");
      return Scaffold(
        body: Container(
          color: Color(0xFFF2F3F5),
          child: Padding(
              padding: EdgeInsets.fromLTRB(0, 10, 0, 0),
              child: Column(
                children: <Widget>[
                  Expanded(
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width,
                        child: GridView.builder(
                            cacheExtent: 300,
                            padding: EdgeInsets.all(9.0), // 设置左右间距为9ß
                            gridDelegate:
                            const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 4,
                                childAspectRatio: 1,
                                mainAxisSpacing: 9,
                                crossAxisSpacing: 9),
                            itemBuilder: (BuildContext context, int index) {
                              return FliggyPhotoAlbumManager().getPhotoItems(
                                  context,
                                  index,
                                  FliggyPhotoAlbumManager().systemAlbumListMap[widget.albumName]!.albumList[index], () {
                                contentViewKey.currentState.setState(() {});
                              });
                            },
                            itemCount: FliggyPhotoAlbumManager()
                                .systemAlbumListMap[widget.albumName]!.albumList.length),
                      )),
                  FliggyPhotoToolView(contentViewKey, 1, AlbumRouter.localPhotoRouter + "/" + widget.albumName)
                ],
              )),
        ),
      );
    } else {
      return FutureBuilder(
        future: FliggyPhotoAlbumManager().getPhotoWithFBridge(context, () {

        }),
          builder: (BuildContext context, AsyncSnapshot snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          FBridgeApi.newInstance(context).call("open_loading_view");
          return Container();
        } else {
          // 数据成功返回后，更新页面
          FBridgeApi.newInstance(context).call("close_loading_view");
          return Scaffold(
            body: Container(
              color: Color(0xFFF2F3F5),
              child: Padding(
                  padding: EdgeInsets.fromLTRB(0, 10, 0, 0),
                  child: Column(
                    children: <Widget>[
                      Expanded(
                          child: SizedBox(
                            width: MediaQuery.of(context).size.width,
                            child: GridView.builder(
                                cacheExtent: 300,
                                padding: EdgeInsets.all(9.0), // 设置左右间距为9
                                gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 4,
                                    childAspectRatio: 1,
                                    mainAxisSpacing: 9,
                                    crossAxisSpacing: 9),
                                itemBuilder: (BuildContext context, int index) {
                                  return FliggyPhotoAlbumManager().getPhotoItems(
                                      context,
                                      index,
                                      FliggyPhotoAlbumManager().systemAlbumListMap[widget.albumName]!.albumList[index], () {
                                    contentViewKey.currentState.setState(() {});
                                  });
                                },
                                itemCount: FliggyPhotoAlbumManager()
                                    .systemAlbumListMap[widget.albumName]!.albumList.length),
                          )),
                      FliggyPhotoToolView(contentViewKey, 1, AlbumRouter.localPhotoRouter + "/" + widget.albumName)
                    ],
                  )),
            ),
          );
        }
      });
    }
  }

  @override
  String getPageName() {
    return AlbumRouter.homeRouter;
  }

  @override
  String getPageSpmCnt() {
    return PicturePickerSpm.allPicPage("0");
  }

  @override
  void dispose() {
    eventBus.off(BusEvents.EVENT_PREVIEW_SELECT_CHANGE,eventCallback);
    super.dispose();
  }
}

