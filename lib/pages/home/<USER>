import 'dart:async';
import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_base_define.dart';
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:flutter_travel_memories_photo_album/pages/all_photo_page/photo_picker_permission_page.dart';
import 'package:flutter_travel_memories_photo_album/pages/map_photo_album_page/map_photo_permission_page.dart';
import 'package:flutter_travel_memories_photo_album/util/fliggy_permission_handler.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_global_config.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/widgets/album_header_indicator_view.dart';
import 'package:flutter_travel_memories_photo_album/pages/all_photo_page/photo_picker_all_page.dart';
import 'package:flutter_travel_memories_photo_album/pages/all_photo_page/photo_picker_album_list_page.dart';
import 'package:flutter_travel_memories_photo_album/pages/map_photo_album_page/map_photo_page_controller.dart';
import 'package:fbridge/fbridge.dart';

// ficonfont依赖
import 'package:ficonfont/ficonfont.dart' as FIcon;

import 'package:fliggy_router/fliggy_router.dart';
import 'package:permission_handler/permission_handler.dart';

/// 图片选择器主页面，同时充当了主控制器，
class FliggyPhotoSelectHomePage extends StatefulWidget {
  final Map? params;

  const FliggyPhotoSelectHomePage({Key? key, this.params})
      : super(key: key);

  @override
  State<FliggyPhotoSelectHomePage> createState() =>
      _FliggyPhotoSelectHomePageState(this.params ?? {});
}

// 主页，包括导航栏和地图相册/全部相册框架
class _FliggyPhotoSelectHomePageState extends State<FliggyPhotoSelectHomePage>
    with TickerProviderStateMixin {
  final Map params;

  List<Widget> headers = [];
  List<Widget> containers = [];

  // 判断在全部相册页面该展示哪个界面
  bool showPhotoList = false;

  // 控制器
  late TabController _tabController;

  // 当前展示的相册的名称
  String currentAlbum = "";

  List<FliggyPictureModel> FliggyPictureModelList = [];

  _FliggyPhotoSelectHomePageState(this.params);

  // 权限申请页面如果有权限时的刷新函数
  Future<void> refresh() async{
    setState(() {});
    await FBridgeApi.newInstance(context).call("open_loading_view");
    FliggyPhotoAlbumManager().getAsignPhotoWithFBridge(context, () async{
      await FBridgeApi.newInstance(context).call("close_loading_view");
      FliggyPhotoAlbumManager().hasGetAlbumFirst = true;
      setState(() {
        if (FliggyPhotoAlbumManager().systemAlbumListMap.length > 0) {
          currentAlbum = FliggyPhotoAlbumManager().systemAlbumListMap.keys.elementAt(0);
        } else {
          // 没有相册，就展示全部相册名称
          currentAlbum = "全部相册";
        }

        print("<fphoto>通知渲染:${DateTime.now()}");
      });
      _refreshAfterSetState();
    });
  }

  void _refreshAfterSetState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 在渲染结束后执行操作
      // 这里可以放置需要在渲染结束后执行的代码
      print("<fphoto>渲染结束后执行操作:${DateTime.now()}");
      FliggyPhotoAlbumManager().getPhotoWithFBridge(context, () {
      });
    });
  }
  // 初始化
  @override
  void initState() {
    super.initState();
    // 后续在这里将服务端传递的参数进行解析
    AlbumGlobalConfig.setConfigFromParams(this.params);
    _tabController = TabController(
      length: AlbumGlobalConfig.keys.length,
      vsync: this,
      initialIndex: AlbumGlobalConfig.keys.length - 1,
    )..addListener(() {
        print('切换tab:${_tabController.index}');
        // 切换tab后刷新页面
        // setState(() {});
      });
    // 初始化配置
    FliggyPhotoAlbumManager().init();
    // 初始赋一个文案,会在展示页面时刷新
    currentAlbum = "全部相册";
    PermissionHandler.checkAndRequirePermissionWithBridge(context,onSuccess: (){
      FliggyPhotoAlbumManager().photoPermission = true;
      // 先获取cache,在 cache 回来后看一下真实数据是否返回
       FBridgeApi.newInstance(null).call2('get_kvcache', {
        "key": 'fliggyPhotoFirstAlbum',
      }).then((value) {
        if (!FliggyPhotoAlbumManager().hasGetAlbumFirst) {
          if (value != null && value.length > 0) {
            FliggyPhotoAlbumManager().getFirstAlbumListMapFromJson(value!);
            if (FliggyPhotoAlbumManager().systemAlbumListMap.length > 0) {
              FliggyPhotoAlbumManager().hasGetAlbumFirst = true;
              currentAlbum = FliggyPhotoAlbumManager().systemAlbumListMap.keys.elementAt(0);
              setState(() {

              });
            }
          }
        }
      }).catchError((e){
        print(e);
      });
      // 真实相册数据
      // 有一个 bug,如果缓存中的相册已经被删了,图片就会访问不到,展示会出错,且点进去会一直等待(除非图片数量为 1)
      // 解决方案:1.在真实数据返回时,清空过去数据
      //         2.在真实数据返回时,与缓存数据合并检查,
      FliggyPhotoAlbumManager().getAsignPhotoWithFBridge(context, () {
        // 真实数据返回:无脑刷新即可
        setState(() {
          if (FliggyPhotoAlbumManager().systemAlbumListMap.length > 0) {
            currentAlbum =
                FliggyPhotoAlbumManager().systemAlbumListMap.keys.elementAt(0);
          } else {
            currentAlbum = '全部相册';
          }
          print("<fphoto>通知渲染:${DateTime.now()}");
        });
        _refreshAfterSetState();
      });

    },onFailed: (){
      FliggyPhotoAlbumManager().photoPermission = false;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    print("<fphoto>开始渲染:${DateTime.now()}");
    return DefaultTabController(
        length: AlbumGlobalConfig.pages.length,
        child: Scaffold(
          appBar: PreferredSize(
              preferredSize: Size.fromHeight(43),
              child: AppBar(
                elevation: 0.0,
                backgroundColor: AlbumGlobalTheme.themeColor,
                centerTitle: true,
                // 左侧返回按钮
                leadingWidth: 36,
                leading: IconButton(
                    padding:EdgeInsets.fromLTRB(18, 8, 0, 8),
                    // 不能用系统自带的icon，只能用iconfont
                      icon:
                      FIcon.Ficon(FIcon.ICON_FANHUIJIANTOU, 22, Colors.black),
                      color: Colors.black,
                      disabledColor: Colors.grey,
                      onPressed: () => popViewAndCallBack()),
                titleSpacing: 0,
                // 顶部中间可选栏
                title: Container(
                  alignment: Alignment.center,
                    // width: AlbumGlobalConfig.keys.length > 1 ? 270 : 80,
                    // color: Color(0xFF123456),
                    child: TabBar(
                      controller: _tabController,
                      isScrollable: true,
                      indicatorColor: AlbumGlobalConfig.keys.length > 1
                          ? Colors.black
                          : Colors.transparent,
                      labelColor: Colors.black,
                      unselectedLabelColor: Color(0xEF000000),
                      // 由于下划线指示器不好用，用自建的下划线指示器
                      indicator: AlbumGlobalConfig.keys.length > 1
                          ? FliggyPhotoAlbumIndicator()
                          : null,
                      tabs: getHeader(),
                    )),
                actions: <Widget>[
                  Container(
                    width: 36,
                  )
                ],
              )),
          body: TabBarView(
            controller: _tabController,
            children: getContainer(),
          ),
        ));
  }

  void popViewAndCallBack() {
    // 增加兜底
    if (FliggyPhotoAlbumManager().callbackData.imgUrls.isEmpty) {
      Map nullMap = {"imgUrls": []};
      FliggyNavigatorApi.getInstance().pop(context, result: nullMap);
    } else {
      Map jsonStr = FliggyPhotoAlbumManager().callbackData.toJson();
      FliggyNavigatorApi.getInstance().pop(context, result: jsonStr);
    }
  }

  /// 动态header
  List<Widget> getHeader() {
    List<Widget> tabs = [];
    //添加地点相册
    if (AlbumGlobalConfig.isNeedCityAlbum) {
      tabs.add(Tab(
          child: Container(
              child: getCityAlbumWidget('isNeedCityAlbum'))));
    }
    //添加全部相册
    if (AlbumGlobalConfig.isNeedPhotoAlbum) {
      tabs.add(
        GestureDetector(
          onTap: () {
            if (_tabController.index == findIndexWithKey('isNeedPhotoAlbum')) {
              showPhotoList = !showPhotoList;
              setState(() {
                // 点击按钮后刷新页面
              });
            } else {
              _tabController.animateTo(findIndexWithKey('isNeedPhotoAlbum'));
            }
          },
          child: Container(
            margin: EdgeInsets.only(left: 10),
              child: getPhotoAlbumWidget('isNeedPhotoAlbum'))));
    }

    this.headers = tabs;
    return tabs;
  }

  List<Widget> getContainer() {
    List<Widget> pages = [];
    //添加地点相册
    if (AlbumGlobalConfig.isNeedCityAlbum) {
      pages.add(cityPage());
    }
    //添加全部相册
    if (AlbumGlobalConfig.isNeedPhotoAlbum) {
      pages.add(photoPage());
    }
    this.containers = pages;
    return pages;
  }

  // 城市相册header
  Widget getCityAlbumWidget(String key) {
    return Container(
        height: 30,
        padding:EdgeInsets.all(0),
        width: 65,
        child: Center(
            child: Text(
          findDataByKey(key)['title'],
          style: TextStyle(fontWeight: FontWeight.bold),
        )));
  }

  // 全部相册顶部header+展开相册按钮
  Widget getPhotoAlbumWidget(String key) {
    List<Widget> photoAlbum = [];
    photoAlbum = [

      ConstrainedBox(constraints: BoxConstraints(maxWidth: 160),
        child: Text(
          currentAlbum,
          softWrap:false,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          // findDataByKey(key)['title'],
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),),
      Container(
          width: 10,
          height: 30,
          child: IconButton(
            padding: EdgeInsets.only(left: 0),
            alignment: Alignment.centerLeft,
            iconSize: 12,
            color: Colors.black,
            onPressed: () {
              if (_tabController.index == findIndexWithKey(key)) {
                showPhotoList = !showPhotoList;
                setState(() {
                  // 点击按钮后刷新页面
                });
              }
            },
            icon: showPhotoList
                ? FIcon.Ficon(FIcon.ICON_SHOUQIJIANTOU, 12, Colors.black)
                : FIcon.Ficon(FIcon.ICON_XIALAJIANTOUXIAO, 12, Colors.black),
          )),
    ];
    return Row(

      children: photoAlbum,
    );
  }

  /// 动态widget，根据key查找page的名称
  findDataByKey(key) {
    for (int i = 0; i < AlbumGlobalConfig.pages.length; i++) {
      Map page = AlbumGlobalConfig.pages[i];
      if (page['key'] == key) {
        return page;
      }
    }
    return null;
  }

  findIndexWithKey(key) {
    int index = AlbumGlobalConfig.keys.indexOf(key);
    return index;
  }

  /// 获取图片相册页面
  Widget photoPage() {
    // 直接判断会导致使用的是兜底、非查询到的结果
    if (!FliggyPhotoAlbumManager().photoPermission) {
      return PhotoPickerPermissionPage(
        refreshFunction: () {
          refresh();
        },
      );
    } else if (FliggyPhotoAlbumManager().hasGetAlbumFirst) {
      // 取过图片桥
      //没有图片
      if (FliggyPhotoAlbumManager().systemAlbumListMap.keys.length <= 0) {
        return
          Scaffold(
              backgroundColor: Color(0xFFFFFFFF),
              body:
              Center(
                  child:
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Image.network(
                        "https://gw.alicdn.com/imgextra/i3/O1CN01CJpdsO1jFII8tvBog_!!6000000004518-0-tps-498-337.jpg",
                        height: 220,
                        width: 220,
                      ),
                      Padding(
                          padding: EdgeInsets.only(top: 0),
                          child: Text(
                            "全部相册为空",
                            style: TextStyle(
                                fontSize: 18,
                                color: Color(0xFF000000),
                                fontWeight: FontWeight.bold),
                          )),
                      Container(
                        padding: EdgeInsets.only(top: 24),
                        height: 90,
                      ),
                    ],
                  )
              ));
      } else {
        // 有图片
        if (showPhotoList) {
          if (FliggyPhotoAlbumManager()
              .systemAlbumListMap
              .containsKey(currentAlbum)) {
            return Stack(
              children: [
                FliggyAllPhotoPage(albumName: currentAlbum),
                FliggyLocalPhotoAlbumListPage(
                  clickCallBack: (String album) {
                    showPhotoList = false;
                    setState(() {
                      currentAlbum = album;
                    });
                  },
                  currentAlbum: currentAlbum,
                ),
              ],
            );
          } else {
            currentAlbum =
                FliggyPhotoAlbumManager().systemAlbumListMap.keys.elementAt(0);
            return FliggyLocalPhotoAlbumListPage(
              clickCallBack: (String album) {
                showPhotoList = false;
                setState(() {
                  currentAlbum = album;
                });
              },
              currentAlbum: currentAlbum,
            );
          }
        } else {
          if (FliggyPhotoAlbumManager()
              .systemAlbumListMap
              .containsKey(currentAlbum)) {
            return FliggyAllPhotoPage(albumName: currentAlbum);
          } else {
            currentAlbum =
                FliggyPhotoAlbumManager().systemAlbumListMap.keys.elementAt(0);
            return FliggyAllPhotoPage(albumName: currentAlbum);
          }
        }
      }
    } else {
      // 没有取相册桥
      return Container();
    }
  }

  /// 获取地点相册页面
  Widget cityPage() {
    if (!FliggyPhotoAlbumManager().photoPermission) {
      return MapPhotoPermissionPage(refreshFunction: () {
        refresh();
      });
    } else {
      return FliggyLocationAlbumController();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
