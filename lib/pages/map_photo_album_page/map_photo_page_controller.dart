import 'package:flutter/cupertino.dart';
import 'package:flutter_travel_memories_photo_album/common/album_events.dart';
import 'package:flutter_travel_memories_photo_album/pages/map_photo_album_page/map_photo_permission_page.dart';
import 'package:flutter_travel_memories_photo_album/pages/map_photo_album_page/map_photo_scan_page.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_global_config.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter/material.dart';
import 'map_photo_album_list_page.dart';


/// 地点相册的bar底下页面太多，不容易控制，这个类只负责控制页面，不做具体展示
class FliggyLocationAlbumController extends StatefulWidget {
  const FliggyLocationAlbumController({Key? key}) : super(key: key);

  @override
  State<FliggyLocationAlbumController> createState() =>
      _FliggyLocationAlbumControllerState();
}

enum ScanState {
  ScanStateNone,
  ScanStateScanning,
  ScanStateDone,
}

class _FliggyLocationAlbumControllerState
    extends State<FliggyLocationAlbumController> {

  @override
  void initState() {
    super.initState();
    // 注册bus
    if (!AlbumGlobalConfig.openedScanCity) {
      // 如果没有开启过，注册小提示弹窗事件
    }
    _registerSetStateEventBus();

  }

  void _registerSetStateEventBus() {
    eventBus.on(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE, (arg) {
      setState(() {

      });
    });
  }
  /// 地点相册的状态管理，后续虽然不在这里使用，但是仍遵循这个原则
  // void _statusChange() {
  //   // 如果缓存数据为空，且开启了权限，且没有扫描过
  //   if (FliggyPhotoAlbumManager.photoPermission) {
  //     // 如果用户没点过，则进开启地点相册页面
  //     if (!AlbumGlobalConfig.openedScanCity) {
  //       FliggyPhotoAlbumManager().mapPhotoPageController = MapPhotoPageState.NO_SCAN_ALBUM_PAGE;
  //     } else {
  //       // 如果接口数据已经返回
  //       if (AlbumGlobalConfig.homeCityDataBack) {
  //         FliggyPhotoAlbumManager().mapPhotoPageController = MapPhotoPageState.NORMAL_ALBUM_PAGE;
  //       } else {
  //         FliggyPhotoAlbumManager().mapPhotoPageController = MapPhotoPageState.CITY_DATA_LOADING;
  //       }
  //     }
  //   } else {
  //     // 没有权限
  //     // 兜底页面
  //     FliggyPhotoAlbumManager().mapPhotoPageController = MapPhotoPageState.NO_PHOTOS_PERMISSION;
  //   }
  // }

  Widget getPageView() {
    if (FliggyPhotoAlbumManager().mapPhotoPageController == MapPhotoPageState.CITY_DATA_LOADING) {
      return MapPhotoScanPage();
    }
    if (AlbumGlobalConfig.openedScanCity == false) {
      return MapPhotoPermissionPage(refreshFunction: () {},);
    } else {
      switch (FliggyPhotoAlbumManager().mapPhotoPageController) {
        case MapPhotoPageState.NO_PHOTOS_PERMISSION:
          return MapPhotoPermissionPage(refreshFunction: () {},);
        case MapPhotoPageState.NORMAL_ALBUM_PAGE:
          return FliggyMapPhotoAlbumListPage();
        case MapPhotoPageState.NO_SCAN_ALBUM_PAGE:
          return MapPhotoPermissionPage(refreshFunction: () {},);
        case MapPhotoPageState.CITY_DATA_LOADING:
          return MapPhotoScanPage();
        case MapPhotoPageState.NET_ERROR_PAGE:
          return Center(
              child: Text(
                "数据异常，请稍后再试",
                style: TextStyle(fontSize: 20),
              ));
        case MapPhotoPageState.NO_CITY_DATA_PAGE:
          return MapPhotoScanPage();
        default:
          // test
        return MapPhotoScanPage();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // _statusChange();
    return getPageView();
  }

  @override
  void dispose() {
    super.dispose();
    eventBus.off(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
    eventBus.off(BusEvents.EVENT_CITY_UPGRATE_FINISHED);
  }
}
