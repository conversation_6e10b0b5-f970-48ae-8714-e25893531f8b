import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/spm/picture_picker_spm.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:flutter_travel_memories_photo_album/widgets/album_photo_tool_view.dart';
import 'map_photo_album_cell.dart';

class FliggyMapPhotoAlbumListPage extends StatefulWidget {
  // final FliggyPhotoAlbumManager photoAlbumManager;

  const FliggyMapPhotoAlbumListPage({Key? key}) : super(key: key);

  @override
  State<FliggyMapPhotoAlbumListPage> createState() =>
      _FliggyMapPhotoAlbumListPageState();
}

class _FliggyMapPhotoAlbumListPageState
    extends State<FliggyMapPhotoAlbumListPage> {
  // 页面入参
  // final FliggyPhotoAlbumManager photoAlbumManager;
  //
  // _FliggyMapPhotoAlbumListPageState({required this.photoAlbumManager});
  bool showUploadStatusCard = false;

  var contentViewKey;
  @override
  void initState() {
    super.initState();
    // 页面埋点
    //import 'package:fliggy_usertrack/fliggy_usertrack.dart';
    //import 'package:flutter_travel_memories_photo_album/spm/picture_picker_spm.dart';
    contentViewKey = new GlobalKey<FliggyPhotoToolViewState>();
    FliggyUserTrackApi.getInstance().pageEnter(context,
        PicturePickerSpm.mapPicListPage("0"), AlbumRouter.mapListRouter,
        args: {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body:
      Column(children: [
      Expanded(
        child:
        Container(
            color: Color(0xFFF2F3F5),
            child: ListView(
                itemExtent:123,
              children: _listView(),
            ))),
        FliggyPhotoToolView(contentViewKey, 1, "")
      ],),
    );
  }

  List<Widget> _listView() {
    List<Widget> listWidget = [];
    FliggyPhotoAlbumManager().cityAlbumListMap.forEach((albumName, value) {
      // 每个数组元素
      listWidget.add(mapPhotoAlbumCell(albumName: albumName));
    });
    return listWidget;
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }
}
