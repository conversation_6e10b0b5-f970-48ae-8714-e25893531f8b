import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:ficonfont/ficonfont.dart' as FIcon;
import 'package:flutter_travel_memories_photo_album/common/album_base_define.dart';
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:flutter_travel_memories_photo_album/widgets/album_photo_tool_view.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';

import '../../common/album_events.dart';

class FliggyMapPhotoPage extends StatefulWidget {
  final Map? params;
  FliggyMapPhotoPage({Key? key,required this.params}) : super(key: key);

  @override
  State<FliggyMapPhotoPage> createState() => _FliggyMapPhotoPageState(this.params ?? {});
}

class _FliggyMapPhotoPageState extends State<FliggyMapPhotoPage> {
  Map pageParams;
  var contentViewKey;
  String cityName = '';

  List<FliggyPictureModel> selectPhoto = [];

  List<Uint8List> thumbnailDatas = [];

  _FliggyMapPhotoPageState(this.pageParams);
//用于处理预览页面的回调
  late void Function(dynamic) eventCallback;
  @override
  void initState() {
    contentViewKey = new GlobalKey<FliggyPhotoToolViewState>();
    super.initState();
    cityName = pageParams['albumName'] ?? '';
    eventCallback = (datas) {
      if (mounted) {
        setState(() {});
      }
    };
    eventBus.on(BusEvents.EVENT_PREVIEW_SELECT_CHANGE,eventCallback);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0.0,
        title: Text(
          cityName,
          style: TextStyle(
              fontSize: 15,
              color: Color(0xff000000),
              fontWeight: FontWeight.bold),
        ),
        backgroundColor: AlbumGlobalTheme.themeColor,
        centerTitle: true,
        leading: IconButton(
          icon: FIcon.Ficon(FIcon.ICON_FANHUIJIANTOU, 22, Colors.black),
          color: Colors.black,
          onPressed: () {
            FliggyNavigatorApi.getInstance().pop(context);
          },
        ),
      ),

      body: Container(
        color: Color(0xFFF2F3F5),
        child: Padding(
            padding: EdgeInsets.fromLTRB(0, 10, 0, 0),
            child: Column(
              children: <Widget>[
                Expanded(
                    child: SizedBox(
                      width: MediaQuery
                          .of(context)
                          .size
                          .width,
                      child: GridView.builder(
                          cacheExtent: 300,
                          padding: EdgeInsets.all(9.0), // 设置左右间距为9
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 4,
                              childAspectRatio: 1,
                              mainAxisSpacing: 9,
                              crossAxisSpacing: 9
                          ),
                          itemBuilder: (BuildContext context, int index) {
                            return FliggyPhotoAlbumManager().getPhotoItems(context,index,FliggyPhotoAlbumManager().cityAlbumListMap[cityName]!.allPicturesList[index],(){
                              contentViewKey.currentState.setState(() {
                              });
                            }
                            );
                          },
                          itemCount: FliggyPhotoAlbumManager().cityAlbumListMap[cityName]!.allPicturesList.length
                      ),
                    )),

                FliggyPhotoToolView(contentViewKey,2,AlbumRouter.mapPhotoRouter+"/"+cityName)
              ],
            )),
      ),
    );
  }
  @override
  void dispose() {
    eventBus.off(BusEvents.EVENT_PREVIEW_SELECT_CHANGE,eventCallback);
    super.dispose();
  }
}

