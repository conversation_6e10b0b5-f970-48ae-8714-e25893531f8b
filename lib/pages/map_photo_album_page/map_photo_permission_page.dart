/// 地点相册中不同的权限申请页面

import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/process/scan_map_album.dart';
import 'package:flutter_travel_memories_photo_album/spm/picture_picker_spm.dart';
import 'package:flutter_travel_memories_photo_album/util/fliggy_permission_handler.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';

// 申请权限
import 'package:permission_handler/permission_handler.dart';

/// 没有权限时申请权限页面
class MapPhotoPermissionPage extends StatefulWidget {
  final refreshFunction;

  const MapPhotoPermissionPage({Key? key, required this.refreshFunction})
      : super(key: key);

  @override
  State<MapPhotoPermissionPage> createState() => _MapPhotoPermissionPageState();
}

class _MapPhotoPermissionPageState extends State<MapPhotoPermissionPage>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();

    FliggyUserTrackApi.getInstance().pageEnter(
        context, PicturePickerSpm.permissionPage("0"), AlbumRouter.homeRouter,
        args: {});
    WidgetsBinding.instance.addObserver(this); // 添加监听器
  }

  /// 当应用程序生命周期发生变化时，会触发该回调函数
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // 当应用程序从后台返回前台时
      // 执行相应操作
      PermissionHandler.checkAndRequirePermissionWithBridge(context,onSuccess: (){
        FliggyPhotoAlbumManager().photoPermission = true;
        widget.refreshFunction();
      },onFailed: (){
        FliggyPhotoAlbumManager().photoPermission = false;
        setState(() {});
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Image.network(
              'https://gw.alicdn.com/imgextra/i3/O1CN018NxU6t1FuZVmpdQNB_!!6000000000547-2-tps-940-900.png',
              height: 220,
              width: 220,
            ),
            Padding(
              padding: EdgeInsets.only(top: 0),
              child: Text(
                "你的照片将以「旅行地」呈现",
                style: TextStyle(
                    fontSize: 18,
                    color: Color(0xFF000000),
                    fontWeight: FontWeight.bold),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 12),
              child: Text(
                "需要开启照片访问权限",
                style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF8D8D8D),
                    fontWeight: FontWeight.w600),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(top: 24),
              child: MaterialButton(
                color: Color(0xFFFFE033),
                disabledColor: Color(0xB2FFD300),
                // 按钮的置灰（不可用状态）要靠这里点击事件为null。。。
                // onPressed: (true) ? () => _test() :null,
                onPressed: () {
                  ScanMapAlbumProcess().requestPermission(widget.refreshFunction,context: context);
                },
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(21),
                ),
                child: Container(
                  width: 249,
                  height: 42,
                  child: Center(
                    child: Text(
                      '立即体验',
                      style: TextStyle(
                        color: Color(0xFF0F131A),
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ),
            )
          ],
        )));
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this); // 移除监听器
    super.dispose();
  }
}
