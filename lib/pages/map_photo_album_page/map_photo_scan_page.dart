/// 地点相册中不同的权限申请页面
///

import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_events.dart';
import 'package:flutter_travel_memories_photo_album/common/album_native_api.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_global_config.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/process/scan_map_album.dart';
import 'package:flutter_travel_memories_photo_album/spm/picture_picker_spm.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:fbridge/fbridge.dart';

import '../../util/kv_utils.dart';

/// 异常情况/没有权限情况的兜底页面
/// 扫描状态在新版交互上其实可以全部由该页面承接，同时减少了以前结构的一些可能出现的bug
///

class MapPhotoScanPage extends StatefulWidget {
  // final refreshFunction;

  const MapPhotoScanPage({Key? key}) : super(key: key);

  @override
  State<MapPhotoScanPage> createState() => _MapPhotoScanPageState();
}

class _MapPhotoScanPageState extends State<MapPhotoScanPage>
    with SingleTickerProviderStateMixin {
  String title = "正在识别旅行地";

  String subTitle = "";

  late AnimationController _animationController;
  late Animation<double> _animation;
  late String imageUrl;

  @override
  void initState() {
    super.initState();
    // todo:埋点
    FliggyUserTrackApi.getInstance().pageEnter(
        context, PicturePickerSpm.permissionPage("0"), AlbumRouter.homeRouter,
        args: {});
    if (FliggyPhotoAlbumManager().mapPhotoPageController ==
        MapPhotoPageState.NO_CITY_DATA_PAGE) {
      imageUrl = "https://gw.alicdn.com/imgextra/i3/O1CN01CJpdsO1jFII8tvBog_!!6000000004518-0-tps-498-337.jpg";
      title = "地点相册为空";
      subTitle = "你去过的地方太神秘了";
    } else {
      imageUrl = "https://gw.alicdn.com/imgextra/i4/O1CN01osR1R41QJRAo0K3WA_!!6000000001955-2-tps-520-450.png";
    }
    // 注册监听事件
    _registerEventBus();

    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 800),
    );

    // 创建动画
    _animation = Tween<double>(begin: 0, end: 0.8).animate(_animationController)
      ..addStatusListener((status) {

        if (status == AnimationStatus.completed) {
          // 动画完成后卡住进度条
          Future.delayed(Duration(milliseconds: 500)).then((_) {
            // 可能页面已经退出了，此时再调用_animationController已经是null了，没有意义了
            if (mounted) {
              _animationController.stop();
            }
          });
        }
      });

    // 开始动画
    _animationController.forward();
  }

  void _registerEventBus() {
    eventBus.on(BusEvents.EVENT_CITY_UPGRATE_FINISHED, (datas) {
      print("EVENT_CITY_UPGRATE_FINISHED: $datas");
      if (datas is int) {
        if (datas <= 0) {
          // 只有第一次弹弹窗
          imageUrl = "https://gw.alicdn.com/imgextra/i3/O1CN01CJpdsO1jFII8tvBog_!!6000000004518-0-tps-498-337.jpg";
          if (!AlbumGlobalConfig.openedScanCity) {
            showScanDialog();
          } else {
            FBridge _bridge = FBridgeApi.newInstance(null);
            _bridge.toast("没有识别到地点", 2000);
          }
          setState(() {
            title = "地点相册为空";
            subTitle = "你去过的地方太神秘了";
          });
        } else {
          FBridge _bridge = FBridgeApi.newInstance(null);
          _bridge.toast("检测到 " + datas.toString() + " 个你出行过的地点", 2000);
          eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
        }
      } else if (datas is String) {
        if (int.parse(datas) <= 0) {
          // 只有第一次弹弹窗
          imageUrl = "https://gw.alicdn.com/imgextra/i3/O1CN01CJpdsO1jFII8tvBog_!!6000000004518-0-tps-498-337.jpg";
          if (!AlbumGlobalConfig.openedScanCity) {
            showScanDialog();
          } else {
            FBridge _bridge = FBridgeApi.newInstance(null);
            _bridge.toast("没有识别到地点", 2000);
          }
          setState (() {
            title = "地点相册为空";
            subTitle = "你去过的地方太神秘了";
          });
        } else {
          FBridge _bridge = FBridgeApi.newInstance(null);
          _bridge.toast("检测到 " + datas.toString() + " 个你出行过的地点", 2000);
          // final task = Future.delayed(Duration(seconds: 5), () {
            // 这里写需要延迟执行的任务
            eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
          // });
        }
      }
      AlbumGlobalConfig.openedScanCity = true;
      // 存 kvcache
      Map params = {"key": "openedScanCity", "value": "true"};
      String jsonStr = json.encode(params);

      // 为啥用这个方法，因为用桥存不进去，查不出来原因，但是问题不大
      FliggyKVUtil.kv.setBool("openedScanCity", true);
    });
  }

  Future<dynamic> showScanDialog() {
    return showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          titlePadding: EdgeInsets.all(0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0), // 设置圆角半径
          ),
          contentPadding: EdgeInsets.all(0),
          title: Container(
            height: 80,
            width: 300,
            padding: EdgeInsets.all(12.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '没有找到旅行地',
                  style: TextStyle(
                      fontSize: 20,
                      color: Color(0xFF0F131A),
                      fontWeight: FontWeight.bold),
                ),
                Expanded(
                  child: Container(
                      child: Align(
                          alignment: Alignment.bottomCenter,
                          child: Text(
                            '旅行时候记得多拍照哦',
                            style: TextStyle(
                                fontSize: 13, color: Color(0xFF8D8D8D)),
                          ))),
                ),
              ],
            ),
          ),
          actions: <Widget>[
            ButtonBar(
              alignment: MainAxisAlignment.spaceBetween, // 将按钮放置在左右两侧
              children: <Widget>[
                MaterialButton(
                    elevation: 0,
                    height: 42,
                    minWidth: 126,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(21),
                    ),
                    color: Color(0xFFF7F8FA),
                    child: Text(
                      "再试一次",
                      style: TextStyle(color: Color(0xFF5C5F66), fontSize: 16),
                    ),
                    disabledColor: Color(0xB2FFD300),
                    // 按钮的置灰（不可用状态）要靠这里点击事件为null。。。
                    onPressed: () {
                      // 系统方法关闭自身
                      if (mounted) {
                        ScanMapAlbumProcess()
                            .requestPermission(() {},context: context);
                        Navigator.of(context).pop();
                        setState(() {
                          title = "正在识别旅行地";

                          subTitle = "检测到 0 个你出行过的地点";
                        });
                      } else {
                        print("授权取消");
                      }
                    }),
                MaterialButton(
                    elevation: 0,
                    height: 42,
                    minWidth: 126,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(21),
                    ),
                    color: Color(0xFFFFE033),
                    child: Text(
                      "好的",
                      style: TextStyle(color: Color(0xFF0F131A), fontSize: 16),
                    ),
                    disabledColor: Color(0xB2FFD300),
                    // 按钮的置灰（不可用状态）要靠这里点击事件为null。。。
                    onPressed: () {
                      // 系统方法关闭自身
                      Navigator.of(context).pop();
                    }),
              ],
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: Center(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Image.network(
              imageUrl,
              height: 220,
              width: 220,
            ),
            Padding(
                padding: EdgeInsets.only(top: 0),
                child: Text(
                  title,
                  style: TextStyle(
                      fontSize: 18,
                      color: Color(0xFF000000),
                      fontWeight: FontWeight.bold),
                )),
            Visibility(
                visible: subTitle.isEmpty ? false : true,
                child: Padding(
                  padding: EdgeInsets.only(top: 21),
                  child: Text(
                    subTitle,
                    style: TextStyle(
                        fontSize: 13,
                        color: Color(0xFF8D8D8D),
                        fontWeight: FontWeight.w600),
                  ),
                )),
            // 进度条后续在这个Container里做，先不做了，只能做一个假的
            Container(
              padding: EdgeInsets.only(top: 24),
              height: 10,
            ),
            Visibility(
                visible: FliggyPhotoAlbumManager().mapPhotoPageController == MapPhotoPageState.CITY_DATA_LOADING,
                child: AnimatedBuilder(
                  animation: _animation,
                  builder: (context, child) {
                    final percent = (_animation.value * 100).toInt();
                    return Text(
                      '$percent%',
                      style:
                          TextStyle(fontSize: 16.0, color: Color(0xFF0F131A)),
                    );
                  },
                )),
            Visibility(
              visible: FliggyPhotoAlbumManager().mapPhotoPageController == MapPhotoPageState.NO_CITY_DATA_PAGE,
                child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  primary: Color(0xFFFFE033),
                    onPrimary:Color(0xFFFFE033),
                    onSurface:Color(0xFFFFE033),
                    elevation:0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(21),
                    )
                ),
              onPressed: () {
                ScanMapAlbumProcess().requestPermission((){
                },context: context);
              },
              child: Container(
                  width: 249,
                  height: 42,
                  child: Center(
                    child: Text(
                      '立即体验',
                      style: TextStyle(
                        color: Color(0xFF0F131A),
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
            ))
          ],
        )));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}
