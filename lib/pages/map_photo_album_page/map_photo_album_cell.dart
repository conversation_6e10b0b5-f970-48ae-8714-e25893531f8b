import 'dart:typed_data';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:flutter_travel_memories_photo_album/common/album_router.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/spm/picture_picker_spm.dart';

class mapPhotoAlbumCell extends StatefulWidget {
  String albumName;

  mapPhotoAlbumCell({Key? key, required this.albumName}) : super(key: key);

  @override
  State<mapPhotoAlbumCell> createState() =>
      _mapPhotoAlbumCellState(this.albumName);
}

class _mapPhotoAlbumCellState extends State<mapPhotoAlbumCell> {
  final String albumName;

  _mapPhotoAlbumCellState(this.albumName);

  Uint8List a = Uint8List.fromList([]);

  //构造cell
  @override
  Widget get _cellContentView {
    return Container(
        height: 123,
        // width: 375,
        padding: EdgeInsets.only(left: 18),
        color: Color(0xFFFFFFFF),
        child: Row(
          children: <Widget>[
            Container(
              width: 75,
              height: 75,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(3)),
              ),
              child: FliggyPhotoAlbumManager().getPhoto(
                  context,
                  FliggyPhotoAlbumManager()
                      .cityAlbumListMap[albumName]!.allPicturesList[0]),
            ),
            Container(
              height: 75,
                child:Padding(
                    padding: EdgeInsets.only(left: 18),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment:MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        // 地点信息
                        Container(
                          // padding: EdgeInsets.only(top: ),
                          child: Text(
                            albumName,
                            style: TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.bold,
                              color: Color(0xff000000),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )
                        ,
                        // 照片数量
                       Text(
                            FliggyPhotoAlbumManager()
                                .cityAlbumListMap[albumName]!.photonumber
                                .toString() + "图",
                            style: TextStyle(
                              fontSize: 13.5,
                              color: Color(0xff8E8E8E),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        // 时间范围
                        Text(
                          FliggyPhotoAlbumManager()
                                .cityAlbumListMap[albumName]!.time,
                            // FliggyPhotoAlbumManager()
                            //     .cityAlbumListMap[albumName]!.time
                            //     .toString(),
                            style: TextStyle(
                              fontSize: 13.5,
                              color: Color(0xff8E8E8E),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                      ],
                    )))

          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      onTap: () {
        // 地点相册列表页的点击埋点
        FliggyUserTrackApi.getInstance().click(context, AlbumRouter.mapListRouter, PicturePickerSpm.mapPicPage("0"), "", {});
        FliggyNavigatorApi.getInstance().push(context, "page://flutter_view/flutter_picture_picker_map/photo", params: {
          "un_flutter": true,
          "flutter_path": AlbumRouter.mapPhotoRouter,
          "albumName": albumName
        });
      },
      title: Column(
        children: <Widget>[
          // 内容视图
          Container(
            // padding: EdgeInsets.symmetric(horizontal: 1.0, vertical: 2.0),
            child: _cellContentView,
          ),
        ],
      ),
    );
  }
}
