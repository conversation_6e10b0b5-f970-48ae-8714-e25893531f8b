import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/net/album_request_upload_photo.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_global_config.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:ftoast/ftoast.dart';
import 'package:fsuper/fsuper.dart';

class UploadPicProcess {
  static final UploadPicProcess _instance = UploadPicProcess._internal();

  //   const MethodChannel _channel = const MethodChannel('flutter_travel_share');

  ///工厂构造函数
  factory UploadPicProcess() {
    return _instance;
  }

  ///构造函数私有化，防止被误创建
  UploadPicProcess._internal();

  ///点击完成，上传图片
  void clickFinish(BuildContext context, int level, String pageName) {
    if (AlbumGlobalConfig.uploadType == 0) {
      uploadOss(context, level, pageName);
    } else if (AlbumGlobalConfig.uploadType == 1) {
      callBackParams(context, level);
    }
  }

  uploadOss(BuildContext context, int level, String pageName) {
    if (FliggyPhotoAlbumManager().selectPhoto.isEmpty) return;
    FToast.toast(
      context,
      toast: FSuper(
        text: "请稍等，正在上传相册",
        style: TextStyle(color: Colors.black),
        padding: EdgeInsets.all(12),
        backgroundColor: Colors.white,
      ),
      duration: 1000,
    );
    FliggyUploadPhoto.uploadPic(
        context, FliggyPhotoAlbumManager().selectPhoto, pageName,
        popnum: level);
  }

  callBackParams(BuildContext context, int level) {
    FliggyPhotoAlbumManager().callBackPics(context, popnum: level);
  }
}
