import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_events.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/spm/picture_picker_spm.dart';
import 'package:flutter_travel_memories_photo_album/util/fliggy_permission_handler.dart';
// 申请权限
import 'package:permission_handler/permission_handler.dart';

class ScanMapAlbumProcess {
  static final ScanMapAlbumProcess _instance = ScanMapAlbumProcess._internal();

  //   const MethodChannel _channel = const MethodChannel('flutter_travel_share');

  ///工厂构造函数
  factory ScanMapAlbumProcess() {
    return _instance;
  }

  ///构造函数私有化，防止被误创建
  ScanMapAlbumProcess._internal();

  void requestPermission(
      VoidCallback refreshFunction,{required BuildContext context}) async {
    FliggyUserTrackApi.getInstance().click(
        context,
        "Page_flutter_photo_permission_page",
        PicturePickerSpm.permissionPage("0"),
        "", {});
    //获取照片
    PermissionHandler.checkPermission(onSuccess: () async {
      FliggyPhotoAlbumManager().photoPermission = true;
      refreshFunction();
      // AlbumGlobalConfig.openedScanCity = true;

      /// 这里要存cache
      FliggyPhotoAlbumManager().mapPhotoPageController =
          MapPhotoPageState.CITY_DATA_LOADING;
      eventBus.emit(BusEvents.NOTICE_MAP_PHOTO_PAGE_SETSTATE);
      await FliggyPhotoAlbumManager().getCityPhotoList(true,context: context);
    }, onFailed: () {
      openAppSettings();
    });
  }
}
