// 给图片上传的埋点使用的格式转化小工具
// 埋点上传强制要求Map<String,String>
import 'dart:io';

import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';

Map<String,String>  FliggyPictureModelListtoMap(List<FliggyPictureModel> selectPhoto){
  Map<String,String> res = {};

  for ( int i = 0;i < selectPhoto.length; i++) {
    if (Platform.isIOS) {
      res[i.toString()] = selectPhoto[i].id;
    } else {
      res[i.toString()] = selectPhoto[i].path;
    }
  }
  return res;
}
