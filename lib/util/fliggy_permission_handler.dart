

import 'dart:io' as io;
import 'dart:io';
import 'package:fbridge/fbridge.dart';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionHandler {
  static Future checkAndRequirePermissionWithBridge(BuildContext context,{VoidCallback? onSuccess,VoidCallback? onFailed}) async {
    FBridgeApi.newInstance(context).call('permission', {
      "action": 'request',
      "permission": 'album'
    }).then((value) {
      if (Platform.isAndroid) {
        FBridgeApi.newInstance(context).call('permission', {
          "action": 'request',
          "permission": 'media_location',
          'ext':{
            'dialog_title':'请开启照片经纬度授权',// 标题
            'dialog_content':'为了读取到照片城市，需要使用照片经纬度',// 副标题
            'dialog_button_positive':'允许', // 确认按钮文案
            'dialog_button_negative':'取消' // 取消按钮文案
          }
        }).then((value) {
          Map res = value;
          bool per = true;
          if (value.containsKey("ret")) {
            per = parseBool(res["ret"]);
          } else {
            per = false;
          }
          if (per) {
            onSuccess != null ? onSuccess() : print("权限申请成功");
          } else {
            onFailed != null ? onFailed() : print("权限申请失败");
          }
        });
      } else {
        Map res = value;
        bool per = true;
        if (value.containsKey("ret")) {
          per = parseBool(res["ret"]);
        } else {
          per = false;
        }
        if (per) {
          onSuccess != null ? onSuccess() : print("权限申请成功");
        } else {
          onFailed != null ? onFailed() : print("权限申请失败");
        }
      }
    }).catchError((onError) {
      onFailed != null ? onFailed() : print("权限申请失败");
    });
  }
  static Future checkAndRequirePermission({VoidCallback? onSuccess,VoidCallback? onFailed}) async {
    if (io.Platform.isIOS) {
      _checkAndRequirePermission(
          permissionList: [Permission.photos],
          onSuccess: onSuccess,
          onFailed: onFailed,
          goSetting: () {});
      // return await Permission.photos.request().isGranted;

    } else {
      _checkAndRequirePermission(
          permissionList: [Permission.storage, Permission.accessMediaLocation],
          onSuccess: onSuccess,
          onFailed: onFailed,
          goSetting: () {});
    }
  }


  static Future checkPermission({VoidCallback? onSuccess,VoidCallback? onFailed}) async {
    if (io.Platform.isIOS) {
      _checkPermission(
          permissionList: [Permission.photos],
          onSuccess: onSuccess,
          onFailed: onFailed,
          goSetting: () {});
      // return await Permission.photos.request().isGranted;

    } else {
      _checkPermission(
          permissionList: [Permission.storage, Permission.accessMediaLocation],
          onSuccess: onSuccess,
          onFailed: onFailed,
          goSetting: () {});
      // return await Permission.storage.request().isGranted;
    }
    // return true;
  }

  /// 获取新列表中的权限 如果有一项不合格就返回false
  static _requestPermission(List<Permission> permissionList) async {
    Map<Permission, PermissionStatus> statuses = await permissionList.request();
    PermissionStatus currentPermissionStatus = PermissionStatus.granted;
    statuses.forEach((key, value) {
      if (!value.isGranted) {
        currentPermissionStatus = value;
        return;
      }
    });
    return currentPermissionStatus;
  }

  /// 检测是否有权限
  /// [permissionList] 权限申请列表
  /// [onSuccess] 全部成功
  /// [onFailed] 有一个失败
  /// [goSetting] 前往设置 插件虽然提供了一个跳转设置的方法不过也可以自定义
  static _checkPermission(
      {required List<Permission> permissionList,
      VoidCallback? onSuccess,
      VoidCallback? onFailed,
      VoidCallback? goSetting}) async {
    ///一个新待申请权限列表
    List<Permission> newPermissionList = [];

    ///遍历当前权限申请列表
    for (Permission permission in permissionList) {
      PermissionStatus status = await permission.status;

      /// 如果不是允许状态就添加到新的申请列表中
      if (!status.isGranted) {
        onFailed != null ? onFailed() : print("权限申请失败");
        return;
      }
    }
    onSuccess != null ? onSuccess() : print("权限申请成功");
  }


  /// 检测是否有权限并申请
  /// [permissionList] 权限申请列表
  /// [onSuccess] 全部成功
  /// [onFailed] 有一个失败
  /// [goSetting] 前往设置 插件虽然提供了一个跳转设置的方法不过也可以自定义
  static _checkAndRequirePermission(
      {required List<Permission> permissionList,
        VoidCallback? onSuccess,
        VoidCallback? onFailed,
        VoidCallback? goSetting}) async {
    ///一个新待申请权限列表
    List<Permission> newPermissionList = [];

    ///遍历当前权限申请列表
    for (Permission permission in permissionList) {
      PermissionStatus status = await permission.status;

      /// 如果不是允许状态就添加到新的申请列表中
      if (!status.isGranted) {
        newPermissionList.add(permission);
      }
    }

    // ///如果需要重新申请的列表不是空的
    if (newPermissionList.isNotEmpty) {
      PermissionStatus permissionStatus = await _requestPermission(newPermissionList);

      switch (permissionStatus) {

        ///拒绝状态
        case PermissionStatus.denied:
          onFailed != null ? onFailed() : print("权限申请失败");
          break;

        ///允许状态
        case PermissionStatus.granted:
        case PermissionStatus.limited:
          onSuccess != null ? onSuccess() : print("权限申请成功");
          break;

        /// 永久拒绝  活动限制
        case PermissionStatus.restricted:
        case PermissionStatus.permanentlyDenied:
          onFailed != null ? onFailed() : print("权限申请失败");
          goSetting != null ? goSetting() : openAppSettings();
          break;
      }
    } else {
      onSuccess != null ? onSuccess() : print("权限申请成功");
    }
  }

  static bool parseBool(String value) {
    if (value.toLowerCase() == 'true' || value.toLowerCase() == 'success') {
      return true;
    } else if (value.toLowerCase() == 'false') {
      return false;
    } else {
      return false;
    }
  }
}