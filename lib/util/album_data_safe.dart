class FliggyAlbumDataSafe {
  ///根据key返回数据，如果为空或者不存在则返回默认
  static dynamic safeMapWithDefault({required Map map, required String key,required dynamic defaultValue}) {
    if (!map.containsKey(key) || map[key] == null) {
      return defaultValue;
    } else {
      return map[key];
    }
  }
}

bool textIsEmpty(String text) {
  return text == null || text.length == 0;
}

String safeString(dynamic s, {String defaultString = ''}) {
  if (!(s is String)) {
    return s?.toString() ?? defaultString;
  }
  return (textIsEmpty(s) || s == "null") ? defaultString : s;
}
