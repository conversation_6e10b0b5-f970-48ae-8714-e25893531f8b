// 相册依赖
// import 'dart:async';
import 'dart:core';
import 'dart:convert';
import 'dart:io';
import 'package:fbridge/fbridge.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/common/album_native_api.dart';
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';

import 'package:flutter_travel_memories_photo_album/models/bridge_callback_data_model.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_global_config.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:fsuper/fsuper.dart';
import 'package:ftoast/ftoast.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:flutter_travel_memories_photo_album/util/list2Map.dart';

class FliggyUploadPhoto {
  // 拼接参数并通过网络上传
  static Future<bool> uploadPhotos(
      BuildContext context, List<FliggyPictureModel> photolist) async {
    List<String> photoPathList = [];
    List<String> idList = [];
    List<String> uploadList = [];
    // 将photo的path地址和ID分别取出并拼接
    for (int i = 0; i < photolist.length; i++) {
      String photoPath = photolist[i].path ?? '';
      String id = photolist[i].id ?? '';
      if (photoPath != '') {
        photoPathList.add(photoPath);
      }
      if (id != '') {
        idList.add(id);
      }
      if (photoPath == '' && id != '') {
        await FBridgeApi.newInstance(context).callSafe('photo_get_url_from_id', {
          "photo_id": id,
        }).then((value) {
          photoPath = value['photo_url'];
        }).catchError((e){
          print(e);
        });
      }

      await FBridgeApi.newInstance(context).callSafe('media_file_upload', {
        "media_type": 'photo',
        "file_url": photoPath,
        // "photo_id": id,
      }).then((value) {
        String uploadUrl = value['upload_url'];
        if (uploadUrl != null) {
          uploadList.add(uploadUrl);
        }

      }).catchError((e){
        print(e);
      });
    }

    FliggyPhotoAlbumManager().callbackData =
    BridgeCallbackDataModel.dataToModel(context, uploadList, photolist);

    return (uploadList.length == photolist.length);
  }

  /// 回传图片参数，type为0时使用
  // static callBackPics(BuildContext context) {
  //   Map res = {"result": "test"};
  //   FliggyNavigatorApi.getInstance().pop(context, result: res);
  // }

  /// 上传图片接口，供各个地方放使用
  static Future<void> uploadPic(BuildContext context,
      List<FliggyPictureModel> selectPhoto, String pageName,
      {int? popnum}) async {
    // 上报用户点击上传图片埋点
    FliggyUserTrackApi.getInstance().custom(context, "uploadPicStartSpm",
        pageName, FliggyPictureModelListtoMap(selectPhoto));
    FBridgeApi.newInstance(context).call("open_loading_view");

    FliggyUploadPhoto upload = new FliggyUploadPhoto();
    uploadPhotos(context, selectPhoto).then((res) {
      FBridgeApi.newInstance(context).call("close_loading_view");
      if (res) {
        FliggyUserTrackApi.getInstance().custom(context, "uploadPicFinishSpm",
            "",{} );
        if (popnum != null) {
          uploadSuccess(context, popnum: popnum);
        } else {
          uploadSuccess(context);
        }
      } else {
        uploadFailed(context);
      }
      // }
    });
  }

  // 上传成功接口
  static void uploadSuccess(BuildContext context, {int? popnum}) {
    // 上传成功,返回调用入口
    // 这行代码实际只是测试用
    if (Platform.isAndroid) {
      if (FliggyPhotoAlbumManager().callbackData.imgUrls.isEmpty) {
        // Map nullMap = {"imgUrls":[]};
        Map res = {"imgUrls": []};
        FliggyNavigatorApi.getInstance()
            .popTo(context, AlbumGlobalConfig.backUrl, result: res);
      } else {
        // Map jsonStr =  FliggyPhotoAlbumManager().callbackData.toJson();
        Map res = FliggyPhotoAlbumManager().callbackData.toJson();
        FliggyNavigatorApi.getInstance()
            .popTo(context, AlbumGlobalConfig.backUrl, result: res);
      }
    } else {
      // 如果是iOS，使用pop
      if (FliggyPhotoAlbumManager().callbackData.imgUrls.isEmpty) {
        Map nullMap = {"imgUrls": []};
        if (popnum == 1) {
          FliggyNavigatorApi.getInstance().pop(context, result: nullMap);
        } else if (popnum == 2) {
          FliggyNavigatorApi.getInstance().pop(context);
          FliggyNavigatorApi.getInstance().pop(context, result: nullMap);
        }
      } else {
        Map jsonStr = FliggyPhotoAlbumManager().callbackData.toJson();
        if (popnum == 1) {
          FliggyNavigatorApi.getInstance().pop(context, result: jsonStr);
        } else if (popnum == 2) {
          FliggyNavigatorApi.getInstance().pop(context);
          Future.delayed(Duration(milliseconds: 300), () {
            FliggyNavigatorApi.getInstance().pop(context, result: jsonStr);
          });
          // FliggyNavigatorApi.getInstance().pop(context, result: jsonStr);
        }
      }
    }
  }

  // 上传照片失败弹窗
  static void uploadFailed(BuildContext context) {
    FliggyUserTrackApi.getInstance().custom(context, "uploadPicCDNFailedSpm",
        "", {});
    FToast.toast(
      context,
      toast: FSuper(
        text: "上传相册失败，请稍后重试",
        style: TextStyle(color: Colors.black),
        padding: EdgeInsets.all(12),
        backgroundColor: Colors.white,
        // shadowColor: Colors.yellow,
        // shadowBlur: 80,
      ),
      duration: 3000,
    );
  }
}
