import 'package:fliggy_mtop/fliggy_mtop.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_travel_memories_photo_album/common/album_network_api.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';

/// 地图数据请求
/// String needSave;  // 是否需要保存足迹，false只查询经纬度
/// String geoLocationJson;   // 城市信息
/// 接口测试地址https://max.m.taobao.com/mtop/testmodule/apitest?apiName=mtop.fliggy.tripzoo.footprint.generateFpByScanAlbum&apiV=1.0&biz_domain=ali
/// 在测试里显示需要登录，但是实际上好像并不需要，或者底层做了。在猪宝宝里是可以运行的

typedef FliggyGetCityDataCallback = Function(MtopResponseModel data);

class FliggyRequestCityData {
  static Future requestCityData({
    required bool needSave,
    required BuildContext context,
    required List<String> geoLocationJson,
    Function(List data)? onSuccess,
    FliggyGetCityDataCallback? onFailed,
  }) async {
    List allData = [];
    Map<int, List> resultMap = {};
    // 对每个分组的经纬度信息做mtop请求
    geoLocationJson.forEach((element) {
      int currentIndex = geoLocationJson.indexOf(element);
      Map params = {'needSave': needSave, 'geoLocationJson': element};

      var requestModel = MtopRequestModel.buildRequset(
          api: AlbumNetworkApi.generateFpByScanAlbum,
          version: '1.0',
          method: "POST",
          params: params);
      FliggyMtopApi.getInstance().send(null, requestModel,
          successHandler: (MtopResponseModel response) {
        // allData.addAll(response.data["model"]["geoQueryAndSaveDTOS"] ?? {});
        resultMap[currentIndex] =
            response.data["model"]["geoQueryAndSaveDTOS"] ?? {};
        if (geoLocationJson.length == resultMap.length) {
          for (int i = 0; i< resultMap.length;i ++) {
            List? item = resultMap[i];
            allData.addAll(item!);
          }
          onSuccess != null ? onSuccess(allData) : print('no callback');
        }
      }, errorHandler: (MtopResponseModel response) {
            FliggyUserTrackApi.getInstance().custom(context, "FlutterPhoto-PartMTPOError",
                "",{"photoList":element} );
        onFailed != null ? onFailed(response) : print('no callback');
      });
    });
  }
}
