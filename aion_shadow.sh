#!/usr/bin/env bash
##!/bin/bash

###
### 欢迎使用 Fliggy aion.sh
###
### 使用:
###   aion.sh <input>
###
### 参数说明:
###   --h            帮助
###   --uppub        刷新pub依赖
###   --deploy       常规发布。能够生成产物上传 cdn 获取 url。必须在 master 分支执行。
###   --deployToOld  兼容发布。当需要向历史版本发布兼容动态包时，使用该命令。
###   --test         生成用于测试的 url，该 url 为覆盖式发布。
###   --test2        生成用于测试的 url，该 url 为非覆盖式发布。
###   --doc          文档库
###   --sync         快速同步 baseline 依赖版本
###   --baseline     查看基线
###   --fdemo        fdemo 二维码
###   --fdemo25      flutter 2.5 的 fdemo 二维码
###   --readme       生成 README.md 模版
###   --c            创建模版工程
###   --ign          更新 .gitignore 文件
###   --count        统计当前项目的代码规模
###   --upflutter    更新本地环境变量中 flutter 接管脚本
###   dart           运行dart脚本，此后的参数会透传
###
###
### 追加参数：在以上参数后追加的参数，直接跟在第一个参数后，如 --deploy nokeep
###   nokeep        关闭自动同步 keep 文件，可使用自定义的 keep 文件
###
### 提示:
###   1. 只有 flutter 为 release/profile 时才能正常运行动态包

help() {
  awk -F'### ' '/^###/ { print $2 }' "$0"
}

HEADER='\033[95m'
OKBLUE='\033[94m'
OKGREEN='\033[92m'
WARNING='\033[93m'
FAIL='\033[91m'
END='\033[0m'
BOLD="\033[1m"

project_name="undefind"
project_path=$(pwd)
project_dir_name=${project_path##*/}
shell_url="http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev"
start_time=$(date +%s)

function logh() {
  echo "$HEADER $1 $END"
}

function logb() {
  echo "$OKBLUE $1 $END"
}

function logg() {
  echo "$OKGREEN $1 $END"
}

function logw() {
  echo "$WARNING $1 $END"
}

function loge() {
  echo "$FAIL $1 $END"
}

function get_time() {
  start=$1
  end=$2

  start_s=$(echo $start | cut -d '.' -f 1)
  end_s=$(echo $end | cut -d '.' -f 1)

  time=$(((10#$end_s - 10#$start_s)))

  echo "⏰ $3耗时: $time s"
}

function get_shell_url() {
  shell_url=$(python3 -c "$(curl -fsSL http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/get_shell_url.py)")
}

function doc() {
  logb "欢迎访问Flutter动态化文档库：\n  https://alidocs.dingtalk.com/i/spaces/nb9XJe661kxvgzyA/overview"
  sleep 1
  open -a /Applications/Google\ Chrome.app/ https://alidocs.dingtalk.com/i/spaces/nb9XJe661kxvgzyA/overview
}

function open_baseline() {
  open -a /Applications/Google\ Chrome.app/ https://fliggy-manager.io.alibaba-inc.com/
}

function uppub() {
  temp_time_start=$(date +%s)
  flutter clean
  flutter pub get
  flutter pub upgrade
  temp_time_end=$(date +%s)
  get_time ${temp_time_start} ${temp_time_end}

  cd build_shell
  temp_time_start=$(date +%s)
  flutter clean
  flutter pub get
  flutter pub upgrade
  temp_time_end=$(date +%s)
  get_time ${temp_time_start} ${temp_time_end}

  cd aion_shell
  temp_time_start=$(date +%s)
  flutter clean
  flutter pub get
  flutter pub upgrade
  temp_time_end=$(date +%s)
  get_time ${temp_time_start} ${temp_time_end}
  get_time ${start_time} ${temp_time_end} "共"
}

function sync_baseline() {
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/sync_baseline.py)"
    rm pubspec.lock
    flutter pub get
}

function fdemo() {
  #    url="https://cdn.fliggy.com/flu/fbridge_demo/test/fly.sql?un_flutter=true&flutter_path=/fbridge_demo/bridge_demo&aion=1&a_ver=-1"
  url="https://cdn.fliggy.com/flu/fdemo/test/fly.sql?un_flutter=true&flutter_path=/fdemo/home&aion=1&a_ver=-1"
  qrencode -l M -t UTF8 -k ${url}
}

function fdemo25() {
  #    url="https://cdn.fliggy.com/flu/fbridge_demo/test/fly.sql?un_flutter=true&flutter_path=/fbridge_demo/bridge_demo&aion=1&a_ver=-1"
  url="https://cdn.fliggy.com/flu/fdemo/0.5.4/fly.sql?un_flutter=true&flutter_path=/fdemo/home&aion=1&a_ver=-1"
  qrencode -l M -t UTF8 -k ${url}
}

function readme() {
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/readme.py)"
}

function runDart() {
  {
    if [ ! -d "dartscript" ]; then
      mkdir "dartscript"
    fi
    cd dartscript
    curl -fsSL http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/dartscript/pubspec.yaml -o pubspec.yaml
    curl -fsSL http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/dartscript/common.dart -o common.dart
    curl -fsSL http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/dartscript/runner.dart -o runner.dart
    curl -fsSL http://gitlab.alibaba-inc.com/fliggy_android/buildscript/raw/dev/flitetools/dartscript/aion.dart -o aion.dart
    flutter pub get
    echo "dart 脚本已更新"
    dart common.dart
    dart aion.dart $1 $2 $3 $4 $5
  } || {
    echo "dart 脚本获取失败"
  }
}

function create() {
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/clone.py)"
}


function gitignore() {
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/update_gitignore.py)"
}

function count() {
    echo ""
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/code_count.py)"
    echo ""
}

function upflutter() {
    echo ""
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/check_fflutter.py)"
    echo ""
}

function prepare_env() {
  c_pyyaml=0
  c_zstd=0
  c_pip3=0
  c_argparse=0
  c_requests=0
  c_autokit=0
  c_fvm=0

  echo "检查执行环境.."

  # check
  if python3 -c "import yaml" >/dev/null 2>&1
  then
    logg "✅ [pyyaml] 已安装!"
  else
    c_pyyaml=1
    logw "[❗️]️️ 监测到 [pyyaml] 未安装，即将开始安装.."
  fi

  if python3 -c "import argparse" >/dev/null 2>&1
  then
    logg "✅ [argparse] 已安装!"
  else
    c_argparse=1
    logw "[❗️]️️ 监测到 [argparse] 未安装，即将开始安装.."
  fi

  if python3 -c "import zstd" >/dev/null 2>&1
  then
    logg "✅ [zstd] 已安装!"
  else
    c_zstd=1
    logw "[❗️]️️ 监测到 [zstd] 未安装，即将开始安装.."
  fi

  if [ $c_pyyaml == 1 ]
  then
    logh "正在检查 [pip3] 是否安装.."
    if "pip3 -h" >/dev/null 2>&1
    then
      logg "✅ [pip3] 已安装!"
    else
      c_pip3=1
      logw "[❗️]️️ 监测到 [pip3] 未安装，即将开始安装.."
    fi
  fi

  if python3 -c "import requests" >/dev/null 2>&1
  then
    logg "✅ [requests] 已安装!"
  else
    c_requests=1
    logw "[❗️]️️ 监测到 [requests] 未安装，即将开始安装.."
  fi

  if "auto" >/dev/null 2>&1
  then
    logg "✅ [auto-kit] 已安装!"
  else
    c_autokit=1
    logw "[❗️]️️ 监测到 [auto-kit] 未安装，即将开始安装.."
    if "tnpm" >/dev/null 2>&1
    then
      echo ""
    else
      logw "[❗️]️️ 监测到 [tnpm] 未安装，即将开始安装.."
      if command -v npm >/dev/null 2>&1; then
          sudo npm install --registry=https://registry.npm.alibaba-inc.com -g tnpm
          logg "🍻 [tnpm] 安装完成!"
      else
          logw "[❗️]️️ 监测到 [npm] 未安装，即将开始安装.."
          if command -v brew >/dev/null 2>&1; then
              brew update
              brew install npm
              logg "🍻 [npm] 安装完成!"

              sudo npm install --registry=https://registry.npm.alibaba-inc.com -g tnpm
              logg "🍻 [tnpm] 安装完成!"
          else
              logw "[❗️]️️ 监测到 [brew] 未安装，即将开始安装.."
              /bin/zsh -c "$(curl -fsSL https://gitee.com/cunkai/HomebrewCN/raw/master/Homebrew.sh)"
              logg "🍻 [brew] 安装完成!"

              brew update
              brew install npm
              logg "🍻 [npm] 安装完成!"

              sudo npm install --registry=https://registry.npm.alibaba-inc.com -g tnpm
              logg "🍻 [tnpm] 安装完成!"
          fi
      fi
    fi
  fi

  if "fvm" >/dev/null 2>&1
  then
    fvm_version_output=$(fvm --version)
    fvm_version=$(echo "$fvm_version_output" | awk '{lines[NR]=$0} END{for (i=NR;i>=1;i--) if ($0 ~ /^[0-9]+\.[0-9]+\.[0-9]+$/) {print $0; exit}}')
    if [ "$fvm_version" = "2.4.1" ]; then
        logg "✅ [fvm] 已安装!"
    else
        c_fvm=1
        logw "[❗️]️️ 监测到 [fvm] 版本不兼容"
        logb "🏃 正在卸载不兼容的 [fvm].."
        brew untap befovy/taps >/dev/null 2>&1
        brew untap leoafarias/fvm >/dev/null 2>&1
        brew uninstall fvm@$fvm_version >/dev/null 2>&1
        logg "👍 不兼容的 [fvm] 卸载完成!"
        logb "🏃 即将开始安装正确的 [fvm].."
    fi
  else
    c_fvm=1
    logw "[❗️]️️ 监测到 [fvm] 未安装，即将开始安装.."
  fi

  # install
  if [[ $c_pip3 == 1 ]]; then
    logb "🏃 正在安装[pip3].."
    curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
    sudo python3 get-pip.py
    rm get-pip.py
    pip3 -V
    logg "🍻 [pip3] 安装完成!"
  fi

  if [[ $c_pyyaml == 1 ]]; then
    logb "🏃 正在安装[pyyaml].."
    pip3 install pyyaml==6.0
    logg "🍻 [pyyaml] 安装完成!"
  fi

  if [[ $c_zstd == 1 ]]; then
    logb "🏃 正在安装[zstd].."
    pip3 install zstd==*******
    logg "🍻 [zstd] 安装完成!"
  fi

  if [[ $c_argparse == 1 ]]; then
    logb "🏃 正在安装[argparse].."
    pip3 install argparse
    logg "🍻 [argparse] 安装完成!"
  fi

  if [[ $c_requests == 1 ]]; then
    logb "🏃 正在安装[requests].."
    pip3 install requests==2.31.0
    logg "🍻 [requests] 安装完成!"
  fi

  if [[ $c_fvm == 1 ]]; then
    logb "🏃 正在安装[fvm].."
    logw "❗️️如出现长时间卡在下载的情况，请尝试开启网络加速，重新运行命令"
    brew tap leoafarias/fvm
    brew install fvm@2.4.1
    brew link --overwrite fvm@2.4.1

    # 获取xcode-select的路径
    xcode_path=$(xcode-select -p)
    # 判断路径是否正确
    if [ "$xcode_path" == "/Applications/Xcode.app/Contents/Developer" ]; then
        echo ""
    else
        xcode-select --install
        logw "️🔨正在安装 xcode 命令行工具，请在安装弹窗中确认.. "
    fi

    if "fvm" >/dev/null 2>&1
    then
      logg "🍻 [fvm] 安装完成!"
    else
      logw "[❗️]️️ fvm 安装失败，请手动安装 fvm@2.4.1 版本。如果你本地已经安装了其它版本，请先卸载。"
    fi
  fi

  if [[ $c_autokit == 1 ]]; then
    logb "🏃 正在安装[auto-kit].."
    tnpm i @ali/auto-kit -g
    logg "🍻 [auto-kit] 安装完成!"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    loge "[❌] 首次初始化，请先运行 'auto set' 命令配置 auto-kit 信息，权限申请：https://alidocs.dingtalk.com/i/nodes/6LeBq413JA9BOdm2iX3Zd242JDOnGvpb"
    exit 1
  fi
}

function prepare_flutter_env() {
  local second_param=$1
  {
    python3 -c "$(curl -fsSL ${shell_url}/flitetools/prepare_flutter_env.py)" "$second_param"
  } || {
    exit 1
  }
  echo "👍 执行环境就绪！"
}


# 重命名example
function rename_example() {
  local old_name=$1
  local new_name=$2

  for subdir in $project_path; do
      # 检查当前子目录中是否存在名为 example 的文件夹
      if [ -d "$subdir/$old_name" ]; then
          # 存在则重命名
          mv "$subdir/$old_name" "$subdir/$new_name"
          echo "rename_example: Renamed '$subdir/$old_name' to '$subdir/$new_name'"
      else
          echo "rename_example: '$subdir/$old_name' does not exist, skipping..."
      fi
  done
}

function get_project_name() {
  {
    project_name=$(python3 -c "$(curl -fsSL ${shell_url}/flitetools/get_proname.py)")
  } || {
    echo ""
  }
}

function get_time(){
    start=$1
    end=$2

    start_s=$(echo $start | cut -d '.' -f 1)
    end_s=$(echo $end | cut -d '.' -f 1)

    time=$(( (10#$end_s - 10#$start_s)))

    echo "⏰ $3耗时: $time s"
    echo ""
}

start_time=`date +%s`
export FAION_START_TIME="$start_time"

# get shell url
get_shell_url

# update aion.sh, actually it's user_aion.sh in remote
{
  curl -fsSL ${shell_url}/flitetools/user_aion.sh -o aion.sh
  echo "🍺 aion.sh 已更新"
} || {
  echo ""
}

# 先清除所有临时文件
rm -f ~/.faion/faion_code_version.txt

# handle param
param=$1
if [[ $1 =~ "--" ]]; then
  p0=$1
  if [[ $p0 == "--h" ]]; then
    help
    exit 1
  elif [[ $p0 == "--doc" ]]; then
    doc
    exit 1
  elif [[ $p0 == "--c" ]]; then
    create
    exit 1
  elif [[ $p0 == "--baseline" ]]; then
    open_baseline
    exit 1
  elif [[ $p0 == "--sync" ]]; then
    sync_baseline
    exit 1
  elif [[ $p0 == "--uppub" ]]; then
    export PUB_CACHE="$HOME/.faion/pub-cache"
    uppub
    exit 1
  elif [[ $p0 == "--fdemo25" ]]; then
    fdemo25
    exit 1
  elif [[ $p0 == "--fdemo" ]]; then
    fdemo
    exit 1
  elif [[ $p0 == "--readme" ]]; then
    readme
    exit 1
  elif [[ $p0 == "--ign" ]]; then
    gitignore
    exit 1
  elif [[ $p0 == "--count" ]]; then
    count
    exit 1
  elif [[ $p0 == "--upflutter" ]]; then
    upflutter
    exit 1
  elif [[ $p0 == "--deploy" ]] || [[ $p0 == "--deployToOld" ]] || [[ $p0 == "--test" ]] || [[ $p0 == "--test2" ]] || [[ $p0 == "--fbase" ]]; then
    export PUB_CACHE="$HOME/.faion/pub-cache"
    param=$p0
  else
    help
    exit 1
  fi
elif [[ $1 == "dart" ]]; then
  runDart $2 $3 $4 $5 $6 $7 $8 $9 ${10}
  exit 1
else
  help
  prepare_env
  prepare_flutter_env $2
  # check fflutter
  python3 -c "$(curl -fsSL ${shell_url}/flitetools/check_fflutter.py)"
  exit 1
fi

help

encoded_dir=$(echo "$project_path" | iconv -f utf-8 -t ascii//TRANSLIT//IGNORE)
if [[ $encoded_dir =~ [^[:ascii:]] ]]; then
    loge "❌ 项目路径不可包含中文字符，请修改路径后重试"
    exit 1
else
    echo ""
fi

#  准备python执行环境
prepare_env
prepare_flutter_env $2

# check fflutter
python3 -c "$(curl -fsSL ${shell_url}/flitetools/check_fflutter.py)"

# if there are no arguments, we just update environment
if [ "$#" -eq 0 ]; then
    echo ""
    echo ""
    logg "🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻"
    logg "🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻 一切就绪！ 🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻"
    logg "🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻🍻"
    echo ""
    echo ""
else
    # 校验工作目录是否正确
    get_project_name
    # fliggy_flutter_hotel 历史原因暂无法规范化命名，该模块自行保证不修改现有工程目录名称进行打包
    if [[ "$project_name" != "fliggy_flutter_hotel" ]]; then
        if [[ "$project_dir_name" != "$project_name" ]]; then
            loge "[❗️] 检测到当前项目目录 ${project_dir_name} 与 项目名称${project_name} 不符，请确保项目目录名称与项目名称一致！"
            exit 1
        fi
    fi
    prepare_env_end_time=`date +%s`
    get_time $start_time $prepare_env_end_time "环境准备"

    logb "🏃 flutter pub get"
    # 如果存在example 重命名为example_aion加速打包，之后恢复
    rename_example "example" "example_aion"
    output=$(flutter pub get 2>&1 | tee /dev/tty)
    echo "$output" | grep "Exception\|failed\|运行错误\|Traceback (" > /dev/null
    if [ $? -eq 0 ]; then
      #exit ""
      # 恢复example
      rename_example "example_aion" "example"
      exit 1
    else
      # 恢复example
      rename_example "example_aion" "example"
      echo ""
#       exit 1
    fi
    pub_get_end_time=`date +%s`
    get_time $prepare_env_end_time $pub_get_end_time "拉取依赖"

    # enter build_shell/
    cd build_shell

    # run main compile logic
    {
      curl -fsSL ${shell_url}/flitetools/aion.sh -o aion.sh
      echo "🍺 已获取最新编译脚本"
    } || {
      echo "❌ 编译脚本更新失败.. 即将用本地脚本编译"
    }
    sh aion.sh $1 $2
fi
