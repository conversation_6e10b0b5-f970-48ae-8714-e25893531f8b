name: aion_shell
description: test

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  aion_sdk:
    sdk: flutter
  flutter_travel_memories_photo_album:
    path: ../../flutter_travel_memories_photo_album
  flutter:
    sdk: flutter

dependency_overrides:
  collection: 1.15.0
  dio: 3.0.10
  fbridge:
    git:
      ref: master
      url: **************************:fliggy_mobile/fbridge.git
  fbroadcast: ^1.1.0
  ficonfont: ^0.0.3
  fliggy_mtop:
    git:
      ref: master
      url: **************************:fliggy_mobile/fliggy_mtop.git
  fliggy_router:
    git:
      ref: master
      url: **************************:fliggy_mobile/fliggy_router.git
  fliggy_usertrack:
    git:
      ref: master
      url: **************************:fliggy_mobile/fliggy_usertrack.git
  floading: ^2.0.0
  flutter_boost:
    git:
      ref: v1.22.4-hotfixed-2.x
      url: **************************:fliggy_mobile/flutter_boost.git
  flutter_common:
    git:
      ref: master
      url: **************************:fliggy_mobile/flutter_common.git
  fsuper: ^2.1.1
  ftoast: ^2.0.0
  petitparser: ^4.3.0
  shared_preferences: 2.0.7
  xml: 5.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
            