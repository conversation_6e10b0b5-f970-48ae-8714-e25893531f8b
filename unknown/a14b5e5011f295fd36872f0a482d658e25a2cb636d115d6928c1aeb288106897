import 'dart:convert' as convert;
import 'dart:io';

import 'package:ficonfont/ficonfont.dart' as FIcon;
import 'package:fliggy_router/fliggy_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_travel_memories_photo_album/models/picture_model.dart';
import 'package:flutter_travel_memories_photo_album/pages/photoManager/album_photo_data_manager.dart';
import 'package:flutter_travel_memories_photo_album/process/upload_pic_process.dart';
import 'package:flutter_travel_memories_photo_album/widgets/album_photo_select.dart';

import '../../common/album_events.dart';

/// 用于预览照片的页面
/// 入参：List<Picture>
/// 点击预览可以预览效果，点击完成可以上传

class FliggyPhotoOnePreviewPage extends StatefulWidget {
  final Map? params;

  FliggyPhotoOnePreviewPage({Key? key, this.params}) : super(key: key);

  @override
  State<FliggyPhotoOnePreviewPage> createState() {
    return _FliggyPhotoOnePreviewPageState(this.params ?? {});
  }
}

class _FliggyPhotoOnePreviewPageState extends State<FliggyPhotoOnePreviewPage> {
  Map pageParams;


  PageController _controller = new PageController();

  late Widget selectPhotoBigWidget;

  late FliggyPictureModel picture;

  double width = 1;

  bool select = false;

  _FliggyPhotoOnePreviewPageState(this.pageParams);

  @override
  void initState() {
    super.initState();

    _controller.addListener(() {});
    dynamic pic = pageParams['pic'];
    if (pic != null) {
      if (pic is String) {
        Map photo = convert.jsonDecode(pic);
        picture = FliggyPictureModel().Map2Model(photo);
      } else if (pic is Map) {
        picture = FliggyPictureModel().Map2Model(pic);
      }
    }

    select = FliggyPhotoAlbumManager().searchPicInList(picture);
    selectPhotoBigWidget = FliggyPhotoAlbumManager()
        .getNativePhoto(context, picture, fitSys: BoxFit.contain);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Color(0xFFFFFFFF),
        appBar: AppBar(
            elevation: 0.0,
            // 标题居中
            centerTitle: true,
            backgroundColor: Color(0xFFFFFFFF),
            // 左侧返回按钮
            leading: Padding(
              padding: EdgeInsets.only(top: 16, left: 8),
              child: GestureDetector(
                onTap: () {
                  FliggyNavigatorApi.getInstance().pop(context);
                },
                child: FIcon.Ficon(
                    FIcon.ICON_FANHUIJIANTOU, 22, Color(0xFF000000)),
              ),
            ),

            // 右侧位置展示
            actions: <Widget>[
              // 加了center才能让container脱离appbar的控制，才能控制高度，否则无法控制高度
              Center(
                  child: Padding(
                padding: EdgeInsets.only(right: 9),
                child: GestureDetector(
                    onTap: () {
                      select = FliggyPhotoAlbumManager().selectItemPhoto(
                          context, picture);
                      eventBus.emit(BusEvents.EVENT_PREVIEW_SELECT_CHANGE, {});
                      setState(() {});
                    },
                    child: FliggyPhotoSelectView(
                      select: select,
                    )),
              )),
            ]
            // title: Text('${titleIndex}/${imageUrlList.length}'),
            ),
        // 主体
        body: Container(
          // color: Color(0xFFFFFFFF),
          child: Column(
            children: <Widget>[
              // 主图
              Center(
                  child: Container(
                width: 375,
                height: 500,
                color: Color(0xFFD2D4D9),
                child: GestureDetector(
                    onTap: () {
                      FliggyNavigatorApi.getInstance().pop(context);
                    },
                    onScaleUpdate: (ScaleUpdateDetails e) {
                      setState(() {
                        if (e.pointerCount == 2) {
                          width = e.scale; //.clamp(0.8, 10)
                        }
                        // width = 300;
                      });
                    },
                    child: selectPhotoBigWidget),
              )),

              // 底部
              // Spacer(),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  // 缩略展示
                  Padding(
                    padding: EdgeInsets.only(top: 19),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Container(
                          height: 82.5, child: buildSmallPageItem(picture)),
                    ),
                  ),

                  // 底部按钮
                  Padding(
                      // 位置需要调整
                      padding: EdgeInsets.only(left: 255, top: 25),
                      child:
                      FliggyPhotoAlbumManager().selectPhoto.length > 0 ?
                      MaterialButton(
                          elevation: 0,
                          height: 35.5,
                          minWidth: 81,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(21),
                          ),
                          color: Color(0xFFFFE033),
                          child: Text(
                            "上传  " +
                                FliggyPhotoAlbumManager().selectPhoto.length.toString(),
                            style: TextStyle(),
                          ),
                          disabledColor: Color(0xFF919499),
                          // 按钮的置灰（不可用状态）要靠这里点击事件为null。。。
                          onPressed: () => UploadPicProcess().clickFinish(context,2,"one_photo_view_page")) :
                      MaterialButton(
                          elevation: 0,
                          height: 35.5,
                          minWidth: 81,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(21),
                          ),
                          color: Color(0xFFF2F3F5),
                          child: Text(
                            "未选中",
                            style: TextStyle(
                                color: Color(0xFF919499),
                                fontSize: 15,
                                fontWeight: FontWeight.w600),
                          ),
                          onPressed: () => null))
                ],
              )
            ],
          ),
        ));
  }

  // 生成预览底部小图
  Widget buildSmallPageItem(FliggyPictureModel picture) {
    return Container(
        padding: EdgeInsets.only(left: 9),
        child: Padding(
          padding: EdgeInsets.only(right: 9),
          child: Container(
              decoration: select ? BoxDecoration(
                  border: new Border.all(color: Color(0xFFFFDD00), width: 6)) : null,
              height: 82.5,
              width: 82.5,
              child: selectPhotoBigWidget),
        ));
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }
}
