name: flutter_travel_memories_photo_album
description: 地点相册
version: 0.0.1
author:
homepage:
app_name: fliggy
baseline: ***********
# 开启或关闭自动同步依赖。不写该参数，默认开启
sync_dep: true
environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter

  # 时间/数学工具类
#  common_utils: ^2.0.0
  permission_handler: ^6.0.1
  intl: 0.17.0
  provider: 6.0.5
  flutter_fliggyKV:
    git:
      url: **************************:fliggy_mobile/flutter_fliggyKV.git
      ref:
        2.0.5

dependency_overrides:
  # --------------- AOT 同步插件依赖区：不要随意修改！ ---------------
  flutter_boost:
    git:
      url: "**************************:fliggy_mobile/flutter_boost.git"
      ref: "4.4.0s"
  xml: 5.3.1
#  shared_preferences: 2.0.7
#  dio: 3.0.10
  collection: 1.15.0
  petitparser: ^4.3.0
  # ---------------      AOT 同步插件依赖区：不要随意修改！     ---------------
  # ---------------*******************************************---------------
  # ---------------*******************************************---------------

  flutter_common:
    git:
      url: '**************************:fliggy_mobile/flutter_common.git'
      ref: '1.1.3'

  fbridge:
    git:
      url: '**************************:fliggy_mobile/fbridge.git'
      ref: '3.7'

  fliggy_mtop:
    git:
      url: '**************************:fliggy_mobile/fliggy_mtop.git'
      ref: 1.2.3

  fliggy_usertrack:
    git:
      url: '**************************:fliggy_mobile/fliggy_usertrack.git'
      ref: '2.0.0v'

  fliggy_router:
    git:
      url: "**************************:fliggy_mobile/fliggy_router.git"
      ref: "2.0.0v"

  fbroadcast:
    git:
      url: **************************:fapi/fbroadcast.git
      ref: 1.3.0
  fsuper: ^2.1.1
  ftoast: ^2.0.0
  ficonfont:
    git:
      url: "**************************:fliggy_mobile/ficonfont.git"
      ref: "1.3.0"

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The androidPackage and pluginClass identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  # 没有Native代码请删掉！
  # 没有Native代码请删掉！
  # 没有Native代码请删掉！
#  plugin:
#    androidPackage: com.taobao.trip.flutter_travel_memories_photo_album
#    pluginClass: FlutterTravelMemoriesPhotoAlbumPlugin

  # To add assets to your plugin package, add an assets section, like this:
#  assets:
#    - assets/
  #  - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages
